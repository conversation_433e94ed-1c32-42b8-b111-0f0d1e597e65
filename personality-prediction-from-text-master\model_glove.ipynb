{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Model based on GloVe feature extraction\n", "## Global Vectors for Word Representation\n", "### https://nlp.stanford.edu/projects/glove/\n", "GloVe is an unsupervised learning algorithm for obtaining vector representations for words. Training is performed on aggregated global word-word co-occurrence statistics from a corpus, and the resulting representations showcase interesting linear substructures of the word vector space."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["<>:9: <PERSON>yntaxWarning: invalid escape sequence '\\p'\n", "<>:10: SyntaxWarning: invalid escape sequence '\\p'\n", "<>:9: <PERSON>yntaxWarning: invalid escape sequence '\\p'\n", "<>:10: SyntaxWarning: invalid escape sequence '\\p'\n", "/tmp/ipykernel_9980/1227264095.py:9: SyntaxWarning: invalid escape sequence '\\p'\n", "  GLOVE_BIG = os.path.join(my_path, \"data\\pretrained\\glove.6B.300d.txt\")\n", "/tmp/ipykernel_9980/1227264095.py:10: SyntaxWarning: invalid escape sequence '\\p'\n", "  GLOVE_SMALL = os.path.join(my_path, \"data\\pretrained\\glove.6B.50d.txt\")\n"]}], "source": ["import essay\n", "import pickle\n", "import numpy as np\n", "import struct\n", "import os\n", "import pandas as pd\n", "\n", "my_path = os.path.abspath(os.path.dirname('glove.6B.300d.txt'))\n", "GLOVE_BIG = os.path.join(my_path, \"data\\pretrained\\glove.6B.300d.txt\")\n", "GLOVE_SMALL = os.path.join(my_path, \"data\\pretrained\\glove.6B.50d.txt\")\n", "encoding=\"utf-8\""]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: 'data/essays/essays2467.p'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mFileNotFoundError\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 4\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;66;03m# load the preprocessed data which we saved\u001b[39;00m\n\u001b[32m      2\u001b[39m \u001b[38;5;66;03m# choose how much data you want to load (2467, 11142 or 89364)\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m4\u001b[39m essays = pickle.load(\u001b[38;5;28;43mopen\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mdata/essays/essays2467.p\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mrb\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m)\n\u001b[32m      5\u001b[39m \u001b[38;5;66;03m#essays = pickle.load(open( \"data/essays/essays11142.p\", \"rb\"))\u001b[39;00m\n\u001b[32m      6\u001b[39m \u001b[38;5;66;03m#essays = pickle.load(open( \"data/essays/essays89364.p\", \"rb\"))\u001b[39;00m\n\u001b[32m      8\u001b[39m \u001b[38;5;28mprint\u001b[39m(\u001b[33m\"\u001b[39m\u001b[33mloaded count of essays:\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;28mlen\u001b[39m(essays))\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Downloads/personality-prediction-from-text-master/venv/lib/python3.12/site-packages/IPython/core/interactiveshell.py:326\u001b[39m, in \u001b[36m_modified_open\u001b[39m\u001b[34m(file, *args, **kwargs)\u001b[39m\n\u001b[32m    319\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m file \u001b[38;5;129;01min\u001b[39;00m {\u001b[32m0\u001b[39m, \u001b[32m1\u001b[39m, \u001b[32m2\u001b[39m}:\n\u001b[32m    320\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mValueError\u001b[39;00m(\n\u001b[32m    321\u001b[39m         \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33mIPython won\u001b[39m\u001b[33m'\u001b[39m\u001b[33mt let you open fd=\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mfile\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m by default \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    322\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mas it is likely to crash IPython. If you know what you are doing, \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    323\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33myou can use builtins\u001b[39m\u001b[33m'\u001b[39m\u001b[33m open.\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    324\u001b[39m     )\n\u001b[32m--> \u001b[39m\u001b[32m326\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mio_open\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfile\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[31mFileNotFoundError\u001b[39m: [Errno 2] No such file or directory: 'data/essays/essays2467.p'"]}], "source": ["# load the preprocessed data which we saved\n", "# choose how much data you want to load (2467, 11142 or 89364)\n", "\n", "essays = pickle.load(open( \"data/essays/essays2467.p\", \"rb\"))\n", "#essays = pickle.load(open( \"data/essays/essays11142.p\", \"rb\"))\n", "#essays = pickle.load(open( \"data/essays/essays89364.p\", \"rb\"))\n", "\n", "print(\"loaded count of essays:\", len(essays))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Vectorizer für Glove"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# reference to http://nadbordrozd.github.io/blog/2016/05/20/text-classification-with-word2vec/\n", "# credit to nadbor \n", "\n", "class MeanEmbeddingVectorizer(object):\n", "    def __init__(self, word2vec):\n", "        self.word2vec = word2vec\n", "        if len(word2vec)>0:\n", "            self.dim=len(word2vec[next(iter(glove_mywords))])\n", "        else:\n", "            self.dim=0\n", "            \n", "    def fit(self, X, y):\n", "        return self \n", "\n", "    def transform(self, X):\n", "        return np.array([\n", "            np.mean([self.word2vec[w] for w in words if w in self.word2vec] \n", "                    or [np.zeros(self.dim)], axis=0)\n", "            for words in X\n", "        ])"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# preparing the vectors - kinda manually... :S"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# load all vectors from all words from the GloVe File downladed from stanford\n", "df = pd.read_csv(GLOVE_SMALL, sep=\" \", quoting=3, header=None)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#load all words from all essays in a list\n", "corpus = []\n", "for e in essays:\n", "    for w in e.words:\n", "        corpus.append(w)\n", "# and put it in a dataframe from this \n", "df_corpus = pd.DataFrame(corpus)\n", "df_corpus"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# inner join all GloVe Words with all words in the essays \n", "df_mywords = df.merge(df_corpus)\n", "df_mywords = df_mywords.drop_duplicates()\n", "df_mywords"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#for the vectorizer we need a dict with all of \"our\" words\n", "df_temp = df_mywords.set_index(0)\n", "glove_mywords = {key: val.values for key, val in df_temp.T.items()}\n", "glove_mywords"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for every essay we save the GloVe Vectors in essay.glove as a dictionary\n", "# 5min on 2400 essays and 300D\n", "\n", "for e in essays:\n", "    df_temp_e = pd.DataFrame(e.words)\n", "    try:\n", "        \n", "        df_temp_e = df_temp_e.merge(df_mywords)\n", "        df_temp_e = df_temp_e.drop_duplicates()\n", "        df_temp_e = df_temp_e.set_index(0)    \n", "        e.glove = {key: val.values for key, val in df_temp_e.T.items()}\n", "    except:\n", "        print(\"error\")\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# save this essay data by converting into OBJECT essay and save with pickle and removing non emotional scentences\n", "filename = \"data/essays/essays_glove\" + \"50\" + \"d_\" + str(len(essays)) + \".p\" \n", "pickle.dump(essays, open( filename, \"wb\"))\n", "print(\"saved\", len(essays), \"entries: in\", filename)\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Split data in train & test"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.model_selection import train_test_split\n", "training, test = train_test_split(essays, test_size=0.20, random_state=42)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["train_x = [x.glove for x in training]\n", "\n", "train_y_cEXT = [x.cEXT for x in training]\n", "train_y_cNEU = [x.cNEU for x in training]\n", "train_y_cAGR = [x.cAGR for x in training]\n", "train_y_cCON = [x.cCON for x in training]\n", "train_y_cOPN = [x.cOPN for x in training]\n", "\n", "\n", "test_x = [x.glove for x in test]\n", "\n", "test_y_cEXT = [x.cEXT for x in test]\n", "test_y_cNEU = [x.cNEU for x in test]\n", "test_y_cAGR = [x.cAGR for x in test]\n", "test_y_cCON = [x.cCON for x in test]\n", "test_y_cOPN = [x.cOPN for x in test]\n", "\n", "train_x = np.array(train_x)\n", "train_y_cEXT = np.array(train_y_cEXT)\n", "train_y_cNEU = np.array(train_y_cNEU)\n", "train_y_cAGR = np.array(train_y_cAGR)\n", "train_y_cCON = np.array(train_y_cCON)\n", "train_y_cOPN = np.array(train_y_cOPN)\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Create Vectorizer for GloVe"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# the vectorizer calculates the MEAN of the vectors of all words \n", "# (that's what they recommend on stanford for a simple approach) \n", "glove_vectorizer = MeanEmbeddingVectorizer(glove_mywords)\n", "\n", "# create mean from our vectors\n", "\n", "train_x_vectors = glove_vectorizer.transform(train_x)\n", "\n", "test_x_vectors = glove_vectorizer.transform(test_x)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["len(train_x_vectors)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# for evaluation save some data for later:\n", "evaluation = []\n", "data = len(essays)\n", "vec_name = \"GloVe\""]}, {"cell_type": "markdown", "metadata": {}, "source": ["# SVM"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn import svm\n", "name = \"svm\"\n", "\n", "print(\"training Extraversion cEXT using SVM...\")\n", "clf_svm_cEXT = svm.SVC(kernel='linear')\n", "clf_svm_cEXT.fit(train_x_vectors, train_y_cEXT)\n", "evaluation.append([data, vec_name, name, \"cEXT\", clf_svm_cEXT.score(test_x_vectors, test_y_cEXT)])\n", "print(\"cEXT score: \", clf_svm_cEXT.score(test_x_vectors, test_y_cEXT))\n", "\n", "try:\n", "    print(\"training Neuroticism cNEU using SVM...\")\n", "    clf_svm_cNEU = svm.SVC(kernel='linear')\n", "    clf_svm_cNEU.fit(train_x_vectors, train_y_cNEU)\n", "    evaluation.append([data, vec_name, name, \"cNEU\", clf_svm_cNEU.score(test_x_vectors, test_y_cNEU)])\n", "    print(\"cNEU score: \", clf_svm_cNEU.score(test_x_vectors, test_y_cNEU))\n", "except:\n", "    print(\"with this data not available (MBTI only 4 dimensions)\")\n", "    \n", "print(\"training Agreeableness cAGR using using SVM...\")\n", "clf_svm_cAGR = svm.SVC(kernel='linear')\n", "clf_svm_cAGR.fit(train_x_vectors, train_y_cAGR)\n", "evaluation.append([data, vec_name, name, \"cAGR\", clf_svm_cAGR.score(test_x_vectors, test_y_cAGR)])\n", "\n", "print(\"cAGR score: \", clf_svm_cAGR.score(test_x_vectors, test_y_cAGR))\n", "\n", "print(\"training Conscientiousness cCON using SVM...\")\n", "clf_svm_cCON = svm.SVC(kernel='linear')\n", "clf_svm_cCON.fit(train_x_vectors, train_y_cCON)\n", "evaluation.append([data, vec_name, name, \"cCON\", clf_svm_cCON.score(test_x_vectors, test_y_cCON)])\n", "print(\"cCON score: \", clf_svm_cCON.score(test_x_vectors, test_y_cCON))\n", "\n", "print(\"training Openness to Experience cOPN using SVM...\")\n", "clf_svm_cOPN = svm.SVC(kernel='linear')\n", "clf_svm_cOPN.fit(train_x_vectors, train_y_cOPN)\n", "evaluation.append([data, vec_name, name, \"cOPN\", clf_svm_cOPN.score(test_x_vectors, test_y_cOPN)])\n", "print(\"cOPN score: \", clf_svm_cOPN.score(test_x_vectors, test_y_cOPN))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Decision Tree"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn import tree\n", "name = \"tree\"\n", "\n", "print(\"training Extraversion cEXT using dec...\")\n", "clf_dec_cEXT = tree.DecisionTreeClassifier()\n", "clf_dec_cEXT.fit(train_x_vectors, train_y_cEXT)\n", "evaluation.append([data, vec_name, name, \"cEXT\", clf_dec_cEXT.score(test_x_vectors, test_y_cEXT)])\n", "\n", "print(\"cEXT score: \", clf_dec_cEXT.score(test_x_vectors, test_y_cEXT))\n", "\n", "try:\n", "    print(\"training Neuroticism cNEU using dec...\")\n", "    clf_dec_cNEU = tree.DecisionTreeClassifier()\n", "    clf_dec_cNEU.fit(train_x_vectors, train_y_cNEU)\n", "    evaluation.append([data, vec_name, name, \"cNEU\", clf_dec_cNEU.score(test_x_vectors, test_y_cNEU)])\n", "    print(\"cNEU score: \", clf_dec_cNEU.score(test_x_vectors, test_y_cNEU))\n", "except:\n", "    print(\"with this data not available (MBTI only 4 dimensions)\")\n", "\n", "print(\"training Agreeableness cAGR using using dec...\")\n", "clf_dec_cAGR = tree.DecisionTreeClassifier()\n", "clf_dec_cAGR.fit(train_x_vectors, train_y_cAGR)\n", "evaluation.append([data, vec_name, name, \"cAGR\", clf_dec_cAGR.score(test_x_vectors, test_y_cAGR)])\n", "print(\"cAGR score: \", clf_dec_cAGR.score(test_x_vectors, test_y_cAGR))\n", "\n", "print(\"training Conscientiousness cCON using dec...\")\n", "clf_dec_cCON = tree.DecisionTreeClassifier()\n", "clf_dec_cCON.fit(train_x_vectors, train_y_cCON)\n", "evaluation.append([data, vec_name, name, \"cCON\", clf_dec_cCON.score(test_x_vectors, test_y_cCON)])\n", "print(\"cCON score: \", clf_dec_cCON.score(test_x_vectors, test_y_cCON))\n", "\n", "print(\"training Openness to Experience cOPN using dec...\")\n", "clf_dec_cOPN = tree.DecisionTreeClassifier()\n", "clf_dec_cOPN.fit(train_x_vectors, train_y_cOPN)\n", "evaluation.append([data, vec_name, name, \"cOPN\", clf_dec_cOPN.score(test_x_vectors, test_y_cOPN)])\n", "print(\"cOPN score: \", clf_dec_cOPN.score(test_x_vectors, test_y_cOPN))\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Naive <PERSON>"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.naive_bayes import GaussianNB\n", "name = \"gNB\"\n", "# clf_gnb = GaussianNB()\n", "# clf_gnb.fit(train_x_vectors, train_y)\n", "\n", "\n", "print(\"training Extraversion cEXT using GaussianNaiveBayes...\")\n", "clf_gnb_cEXT = GaussianNB()\n", "clf_gnb_cEXT.fit(train_x_vectors, train_y_cEXT)\n", "evaluation.append([data, vec_name, name, \"cEXT\", clf_gnb_cEXT.score(test_x_vectors, test_y_cEXT)])\n", "print(\"cEXT score: \", clf_gnb_cEXT.score(test_x_vectors, test_y_cEXT))\n", "\n", "try:\n", "    print(\"training Neuroticism cNEU using GaussianNaiveBayes...\")\n", "    clf_gnb_cNEU = GaussianNB()\n", "    clf_gnb_cNEU.fit(train_x_vectors, train_y_cNEU)\n", "    evaluation.append([data, vec_name, name, \"cNEU\", clf_gnb_cNEU.score(test_x_vectors, test_y_cNEU)])\n", "    print(\"cNEU score: \", clf_gnb_cNEU.score(test_x_vectors, test_y_cNEU))\n", "except:\n", "    print(\"with this data not available (MBTI only 4 dimensions)\")\n", "\n", "    \n", "print(\"training Agreeableness cAGR using using GaussianNaiveBayes...\")\n", "clf_gnb_cAGR = GaussianNB()\n", "clf_gnb_cAGR.fit(train_x_vectors, train_y_cAGR)\n", "evaluation.append([data, vec_name, name, \"cAGR\", clf_gnb_cAGR.score(test_x_vectors, test_y_cAGR)])\n", "print(\"cAGR score: \", clf_gnb_cAGR.score(test_x_vectors, test_y_cAGR))\n", "\n", "print(\"training Conscientiousness cCON using GaussianNaiveBayes...\")\n", "clf_gnb_cCON = GaussianNB()\n", "clf_gnb_cCON.fit(train_x_vectors, train_y_cCON)\n", "evaluation.append([data, vec_name, name, \"cCON\", clf_gnb_cCON.score(test_x_vectors, test_y_cCON)])\n", "print(\"cCON score: \", clf_gnb_cCON.score(test_x_vectors, test_y_cCON))\n", "\n", "print(\"training Openness to Experience cOPN using GaussianNaiveBayes...\")\n", "clf_gnb_cOPN = GaussianNB()\n", "clf_gnb_cOPN.fit(train_x_vectors, train_y_cOPN)\n", "evaluation.append([data, vec_name, name, \"cOPN\", clf_gnb_cOPN.score(test_x_vectors, test_y_cOPN)])\n", "print(\"cOPN score: \", clf_gnb_cOPN.score(test_x_vectors, test_y_cOPN))\n", "\n"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Logisic Regression"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.linear_model import LogisticRegression\n", "name=\"logR\"\n", "print(\"training Extraversion cEXT using Logistic Regression...\")\n", "clf_log_cEXT = LogisticRegression(solver=\"newton-cg\")\n", "clf_log_cEXT.fit(train_x_vectors, train_y_cEXT)\n", "evaluation.append([data, vec_name, name, \"cEXT\", clf_log_cEXT.score(test_x_vectors, test_y_cEXT)])\n", "print(\"cEXT score: \", clf_log_cEXT.score(test_x_vectors, test_y_cEXT))\n", "\n", "try:\n", "    print(\"training Neuroticism cNEU using Logistic Regression...\")\n", "    clf_log_cNEU = LogisticRegression(solver=\"newton-cg\")\n", "    clf_log_cNEU.fit(train_x_vectors, train_y_cNEU)\n", "    evaluation.append([data, vec_name, name, \"cNEU\", clf_log_cNEU.score(test_x_vectors, test_y_cNEU)])\n", "    print(\"cNEU score: \", clf_log_cNEU.score(test_x_vectors, test_y_cNEU))\n", "except:\n", "    print(\"with this data not available (MBTI only 4 dimensions)\")\n", "    \n", "print(\"training Agreeableness cAGR using using Logistic Regression...\")\n", "clf_log_cAGR = LogisticRegression(solver=\"newton-cg\")\n", "clf_log_cAGR.fit(train_x_vectors, train_y_cAGR)\n", "evaluation.append([data, vec_name, name, \"cAGR\", clf_log_cAGR.score(test_x_vectors, test_y_cAGR)])\n", "print(\"cAGR score: \", clf_log_cAGR.score(test_x_vectors, test_y_cAGR))\n", "\n", "print(\"training Conscientiousness cCON using Logistic Regression...\")\n", "clf_log_cCON = LogisticRegression(solver=\"newton-cg\")\n", "clf_log_cCON.fit(train_x_vectors, train_y_cCON)\n", "evaluation.append([data, vec_name, name, \"cCON\", clf_log_cCON.score(test_x_vectors, test_y_cCON)])\n", "print(\"cCON score: \", clf_log_cCON.score(test_x_vectors, test_y_cCON))\n", "\n", "print(\"training Openness to Experience cOPN using Logistic Regression...\")\n", "clf_log_cOPN = LogisticRegression(solver=\"newton-cg\")\n", "clf_log_cOPN.fit(train_x_vectors, train_y_cOPN)\n", "evaluation.append([data, vec_name, name, \"cOPN\", clf_log_cOPN.score(test_x_vectors, test_y_cOPN)])\n", "print(\"cOPN score: \", clf_log_cOPN.score(test_x_vectors, test_y_cOPN))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Random Forest"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from sklearn.ensemble import RandomForestClassifier\n", "name=\"RF\"\n", "\n", "\n", "print(\"training Extraversion cEXT using Random Forest...\")\n", "clf_rf_cEXT = RandomForestClassifier(n_estimators=100)\n", "clf_rf_cEXT.fit(train_x_vectors, train_y_cEXT)\n", "evaluation.append([data, vec_name, name, \"cEXT\", clf_rf_cEXT.score(test_x_vectors, test_y_cEXT)])\n", "print(\"cEXT score: \", clf_rf_cEXT.score(test_x_vectors, test_y_cEXT))\n", "\n", "try:\n", "    print(\"training Neuroticism cNEU using Random Forest...\")\n", "    clf_rf_cNEU = RandomForestClassifier(n_estimators=100)\n", "    clf_rf_cNEU.fit(train_x_vectors, train_y_cNEU)\n", "    evaluation.append([data, vec_name, name, \"cNEU\", clf_rf_cNEU.score(test_x_vectors, test_y_cNEU)])\n", "    print(\"cNEU score: \", clf_rf_cNEU.score(test_x_vectors, test_y_cNEU))\n", "except:\n", "    print(\"with this data not available (MBTI only 4 dimensions)\")\n", "\n", "print(\"training Agreeableness cAGR using using Random Forest...\")\n", "clf_rf_cAGR = RandomForestClassifier(n_estimators=100)\n", "clf_rf_cAGR.fit(train_x_vectors, train_y_cAGR)\n", "evaluation.append([data, vec_name, name, \"cAGR\", clf_rf_cAGR.score(test_x_vectors, test_y_cAGR)])\n", "print(\"cAGR score: \", clf_rf_cAGR.score(test_x_vectors, test_y_cAGR))\n", "\n", "print(\"training Conscientiousness cCON using Random Forest...\")\n", "clf_rf_cCON = RandomForestClassifier(n_estimators=100)\n", "clf_rf_cCON.fit(train_x_vectors, train_y_cCON)\n", "evaluation.append([data, vec_name, name, \"cCON\", clf_rf_cCON.score(test_x_vectors, test_y_cCON)])\n", "print(\"cCON score: \", clf_rf_cCON.score(test_x_vectors, test_y_cCON))\n", "\n", "print(\"training Openness to Experience cOPN using Random Forest...\")\n", "clf_rf_cOPN = RandomForestClassifier(n_estimators=100)\n", "clf_rf_cOPN.fit(train_x_vectors, train_y_cOPN)\n", "evaluation.append([data, vec_name, name, \"cOPN\", clf_rf_cOPN.score(test_x_vectors, test_y_cOPN)])\n", "print(\"cOPN score: \", clf_rf_cOPN.score(test_x_vectors, test_y_cOPN))\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["filename = \"data/evaluation/evaluation\" + str(data) + vec_name + \".p\"\n", "pickle.dump(evaluation, open(filename, \"wb\"))\n", "print(\"evaluation saved as\", filename)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["print(evaluation)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}