
#!/usr/bin/env python3
"""
Simple Personality Predictor

This module provides a simplified personality prediction system that doesn't rely on NLTK.
"""

import pickle
import re
import warnings
import numpy as np

# Suppress scikit-learn warnings about unpickling from older versions
warnings.filterwarnings("ignore", category=UserWarning)

class SimplePersonalityPredictor:
    def __init__(self, models_path="data/models/"):
        """
        Initialize the personality predictor by loading the models.

        Args:
            models_path (str): Path to the directory containing the model files
        """
        self.models_path = models_path
        self.load_models()
        self.initialize_lexicons()

    def load_models(self):
        """Load the pre-trained models."""
        try:
            # Load all models except cNEU which has compatibility issues
            self.cEXT = pickle.load(open(f"{self.models_path}cEXT.p", "rb"))
            self.cAGR = pickle.load(open(f"{self.models_path}cAGR.p", "rb"))
            self.cCON = pickle.load(open(f"{self.models_path}cCON.p", "rb"))
            self.cOPN = pickle.load(open(f"{self.models_path}cOPN.p", "rb"))
            self.vectorizer_31 = pickle.load(open(f"{self.models_path}vectorizer_31.p", "rb"))
            self.vectorizer_30 = pickle.load(open(f"{self.models_path}vectorizer_30.p", "rb"))

            print("Models loaded successfully!")

        except Exception as e:
            print(f"Error loading models: {e}")
            raise

    def initialize_lexicons(self):
        """Initialize lexicons for text-based trait estimation."""
        # Lexicons for Neuroticism estimation
        self.neuroticism_high = [
            'worry', 'anxious', 'nervous', 'stress', 'stressed', 'tense', 'afraid',
            'fearful', 'scared', 'frightened', 'panic', 'depressed', 'sad', 'unhappy',
            'miserable', 'gloomy', 'upset', 'distressed', 'irritable', 'angry', 'annoyed',
            'moody', 'touchy', 'temperamental', 'impatient', 'frustrated', 'vulnerable',
            'sensitive', 'emotional', 'insecure', 'inadequate', 'inferior', 'helpless',
            'overwhelmed', 'self-conscious', 'embarrassed', 'ashamed', 'guilty'
        ]

        self.neuroticism_low = [
            'calm', 'relaxed', 'peaceful', 'tranquil', 'serene', 'composed', 'collected',
            'steady', 'stable', 'balanced', 'content', 'satisfied', 'happy', 'cheerful',
            'optimistic', 'positive', 'confident', 'secure', 'comfortable', 'resilient',
            'tough', 'hardy', 'strong', 'brave', 'courageous', 'fearless', 'rational',
            'reasonable', 'logical', 'level-headed', 'even-tempered', 'patient', 'tolerant'
        ]

        # Lexicons for Extraversion correction
        self.extraversion_high = [
            'outgoing', 'sociable', 'social', 'talkative', 'chatty', 'energetic', 'active',
            'enthusiastic', 'excited', 'cheerful', 'lively', 'dynamic', 'assertive', 'bold',
            'confident', 'adventurous', 'daring', 'spontaneous', 'impulsive', 'expressive',
            'gregarious', 'friendly', 'warm', 'affectionate', 'people', 'party', 'parties',
            'group', 'groups', 'team', 'teams', 'crowd', 'crowds', 'gathering', 'gatherings',
            'social', 'socialize', 'socializing', 'extrovert', 'extroverted'
        ]

        self.extraversion_low = [
            'quiet', 'reserved', 'shy', 'introverted', 'introvert', 'private', 'solitary',
            'independent', 'reflective', 'thoughtful', 'contemplative', 'introspective',
            'self-contained', 'self-sufficient', 'alone', 'solitude', 'peace', 'peaceful',
            'calm', 'silent', 'book', 'books', 'reading', 'read', 'think', 'thinking',
            'thoughts', 'inner', 'internal', 'personal', 'intimate', 'deep', 'meaningful',
            'one-on-one', 'individual', 'individuals', 'few', 'small', 'quiet', 'silence'
        ]

        # MBTI related keywords
        self.mbti_keywords = {
            'E': self.extraversion_high + ['lead', 'leading', 'action', 'interact', 'interacting',
                  'external', 'outward', 'engage', 'engaging', 'expressive', 'speak', 'speaking',
                  'talk', 'talking', 'share', 'sharing', 'connect', 'connecting', 'connection'],
            'I': self.extraversion_low + ['reflect', 'reflecting', 'reflection', 'observe', 'observing',
                  'internal', 'inward', 'depth', 'deep', 'reserve', 'reserved', 'listen', 'listening',
                  'contemplate', 'contemplation', 'ponder', 'pondering', 'meditate', 'meditation'],
            'S': ['detail', 'specific', 'concrete', 'practical', 'realistic', 'factual',
                  'present', 'actual', 'real', 'tangible', 'literal', 'exact', 'precise',
                  'sensible', 'grounded', 'hands-on', 'pragmatic', 'direct', 'straightforward',
                  'step', 'steps', 'sequential', 'methodical', 'experience', 'experienced'],
            'N': ['abstract', 'theoretical', 'conceptual', 'idea', 'ideas', 'possibility',
                  'possibilities', 'future', 'potential', 'imagine', 'imagination', 'creative',
                  'innovative', 'novel', 'new', 'pattern', 'patterns', 'connection', 'connections',
                  'vision', 'visionary', 'insight', 'insightful', 'meaning', 'meaningful', 'dream',
                  'dreamer', 'big picture', 'intuition', 'intuitive', 'inspiration', 'inspired'],
            'T': ['logical', 'rational', 'analytical', 'objective', 'impartial', 'fair', 'just',
                  'reason', 'reasoning', 'analysis', 'critique', 'critical', 'truth', 'principle',
                  'principles', 'system', 'systematic', 'consistent', 'efficiency', 'efficient',
                  'problem', 'solve', 'solving', 'solution', 'logic', 'fact', 'facts', 'data',
                  'evidence', 'proven', 'proof', 'judgment', 'clarity', 'clear', 'direct'],
            'F': ['feeling', 'feelings', 'emotion', 'emotions', 'emotional', 'empathy', 'empathetic',
                  'sympathetic', 'compassionate', 'caring', 'harmony', 'harmonious', 'value', 'values',
                  'personal', 'subjective', 'people', 'relationship', 'relationships', 'heart',
                  'authentic', 'authenticity', 'genuine', 'kind', 'kindness', 'compassion',
                  'understanding', 'support', 'supportive', 'care', 'caring', 'nurture', 'nurturing',
                  'connect', 'connection', 'meaningful', 'meaning', 'deep', 'depth'],
            'J': ['organized', 'structure', 'structured', 'plan', 'planning', 'scheduled', 'routine',
                  'systematic', 'methodical', 'orderly', 'neat', 'tidy', 'prepared', 'decisive',
                  'decided', 'settled', 'resolved', 'fixed', 'definite', 'certain', 'sure',
                  'control', 'controlled', 'manage', 'managing', 'management', 'goal', 'goals',
                  'achieve', 'achievement', 'accomplish', 'accomplishment', 'complete', 'completion',
                  'finish', 'finished', 'deadline', 'schedule', 'punctual', 'reliable', 'dependable'],
            'P': ['flexible', 'adaptable', 'spontaneous', 'open-ended', 'casual', 'relaxed', 'easygoing',
                  'go with the flow', 'improvise', 'improvisation', 'option', 'options', 'possibility',
                  'possibilities', 'explore', 'exploring', 'discovery', 'discover', 'curious', 'curiosity',
                  'adventure', 'adventurous', 'freedom', 'free', 'open', 'openness', 'change', 'changing',
                  'adapt', 'adapting', 'flow', 'flowing', 'journey', 'experience', 'experiencing',
                  'moment', 'moments', 'present', 'now', 'play', 'playful', 'spontaneity']
        }

    def predict_big_five(self, text):
        """
        Predict Big Five personality traits with improved accuracy.

        Args:
            text (str): The input text to analyze

        Returns:
            list: Predicted values for [EXT, NEU, AGR, CON, OPN]
                  where 1 = High, 0 = Low
        """
        # Split text into sentences
        sentences = re.split("(?<=[.!?]) +", text)

        # Transform text using vectorizers
        text_vector_31 = self.vectorizer_31.transform(sentences)

        # Get model predictions
        EXT_model = self.cEXT.predict(text_vector_31)
        AGR = self.cAGR.predict(text_vector_31)
        CON = self.cCON.predict(text_vector_31)
        OPN = self.cOPN.predict(text_vector_31)

        # Improve Extraversion prediction using lexical analysis
        EXT = self._analyze_extraversion(text, EXT_model[0])

        # Estimate Neuroticism using lexical analysis
        NEU = self._analyze_neuroticism(text)

        return [int(EXT), int(NEU), int(AGR[0]), int(CON[0]), int(OPN[0])]

    def _simple_tokenize(self, text):
        """
        Simple tokenization function that doesn't rely on NLTK.

        Args:
            text (str): The input text

        Returns:
            list: List of tokens
        """
        # Convert to lowercase
        text = text.lower()

        # Replace punctuation with spaces
        for char in '.,;:!?()[]{}"\'-_':
            text = text.replace(char, ' ')

        # Split on whitespace and filter out empty tokens
        tokens = [token for token in text.split() if token]

        return tokens

    def _analyze_extraversion(self, text, model_prediction):
        """
        Analyze text for extraversion markers to correct model bias.

        Args:
            text (str): The input text
            model_prediction: The model's prediction (0 or 1)

        Returns:
            int: Corrected prediction (0 or 1)
        """
        # Tokenize text
        tokens = self._simple_tokenize(text)

        # Count extraversion and introversion markers
        high_count = sum(1 for word in tokens if word in self.extraversion_high)
        low_count = sum(1 for word in tokens if word in self.extraversion_low)

        # If there's a clear signal in the text, use it
        if high_count > low_count * 1.5:
            return 1  # High extraversion
        elif low_count > high_count * 1.5:
            return 0  # Low extraversion

        # If the text doesn't give a clear signal, use the model's prediction
        return model_prediction

    def _analyze_neuroticism(self, text):
        """
        Analyze text for neuroticism markers with improved accuracy.

        Args:
            text (str): The input text

        Returns:
            int: Estimated neuroticism (0 = Low, 1 = High)
        """
        # Tokenize text
        tokens = self._simple_tokenize(text)

        # Count neuroticism markers
        high_count = sum(1 for word in tokens if word in self.neuroticism_high)
        low_count = sum(1 for word in tokens if word in self.neuroticism_low)

        # Look for phrases that strongly indicate neuroticism
        high_phrases = ['feel anxious', 'get nervous', 'worry about', 'stressed out',
                        'feel insecure', 'fear of', 'afraid of', 'get upset', 'easily hurt']

        # Look for phrases that strongly indicate emotional stability (low neuroticism)
        low_phrases = ['stay calm', 'remain calm', 'handle stress', 'emotionally stable',
                       'not worried', 'rarely worry', 'confident in', 'comfortable with',
                       'at ease', 'feel secure']

        # Check for high neuroticism phrases
        for phrase in high_phrases:
            if phrase in text.lower():
                high_count += 2

        # Check for low neuroticism phrases
        for phrase in low_phrases:
            if phrase in text.lower():
                low_count += 2

        # Context-based analysis
        # If text mentions being calm under pressure or handling stress well, it's a strong indicator of low neuroticism
        if any(stress_word in tokens for stress_word in ['stress', 'pressure', 'challenge', 'difficult']) and \
           any(calm_word in tokens for calm_word in ['calm', 'handle', 'manage', 'cope', 'deal']):
            low_count += 3

        # If text mentions frequent worry or anxiety, it's a strong indicator of high neuroticism
        if any(freq_word in tokens for freq_word in ['always', 'often', 'frequently', 'constantly']) and \
           any(worry_word in tokens for worry_word in ['worry', 'anxious', 'nervous', 'afraid']):
            high_count += 3

        # Determine neuroticism level with a slight bias toward low (to correct previous bias)
        if high_count > low_count + 1:  # Require stronger evidence for high neuroticism
            return 1  # High neuroticism
        else:
            return 0  # Low neuroticism

    def predict_mbti(self, text):
        """
        Predict MBTI personality type from text with improved accuracy.

        Args:
            text (str): The input text

        Returns:
            str: MBTI type (e.g., 'INTJ', 'ENFP')
        """
        # Tokenize text
        tokens = self._simple_tokenize(text)
        text_lower = text.lower()

        # Count occurrences of MBTI-related keywords
        counts = {
            'E': sum(1 for word in tokens if word in self.mbti_keywords['E']),
            'I': sum(1 for word in tokens if word in self.mbti_keywords['I']),
            'S': sum(1 for word in tokens if word in self.mbti_keywords['S']),
            'N': sum(1 for word in tokens if word in self.mbti_keywords['N']),
            'T': sum(1 for word in tokens if word in self.mbti_keywords['T']),
            'F': sum(1 for word in tokens if word in self.mbti_keywords['F']),
            'J': sum(1 for word in tokens if word in self.mbti_keywords['J']),
            'P': sum(1 for word in tokens if word in self.mbti_keywords['P'])
        }

        # Apply weighting to certain dimensions based on context
        # For F/T dimension, give extra weight to emotional and value-based language
        if any(word in tokens for word in ['feel', 'feeling', 'feelings', 'emotion', 'emotions',
                                          'heart', 'value', 'values', 'authentic', 'authenticity',
                                          'kind', 'kindness', 'compassion', 'deep', 'deeply',
                                          'love', 'care', 'caring', 'believe', 'believes', 'believing',
                                          'together', 'shine', 'lift', 'lifting', 'support']):
            counts['F'] += 3  # Increased weight for Feeling

        # For N/S dimension, give extra weight to meaning, imagination, and abstract concepts
        if any(word in tokens for word in ['meaning', 'meaningful', 'imagine', 'imagination',
                                          'abstract', 'vision', 'dream', 'intuition', 'intuitive']):
            counts['N'] += 2

        # Give extra weight to sensing for action-oriented, concrete, real-world focus
        if any(word in tokens for word in ['action', 'real-world', 'results', 'moving', 'solving',
                                          'hands-on', 'practical', 'tangible', 'concrete']):
            counts['S'] += 3

        # Strong S indicators in phrases
        if 'real-world results' in text_lower or 'figuring things out' in text_lower:
            counts['S'] += 5

        # For P/J dimension, give extra weight to adaptability and spontaneity
        if any(word in tokens for word in ['adapt', 'adaptable', 'flow', 'flexible', 'spontaneous',
                                          'open', 'explore', 'journey', 'moment', 'moments', 'spot',
                                          'jumping', 'figuring', 'don\'t bother']):
            counts['P'] += 3  # Increased weight for P indicators

        # Additional check for action-oriented, in-the-moment text (strong P indicator)
        if 'on the spot' in text_lower or 'jumping into action' in text_lower:
            counts['P'] += 5

        # Special check for ENFP indicators in text
        if ('potential everywhere' in text_lower or 'exploring new ideas' in text_lower or
            'follow my heart' in text_lower or 'path is messy' in text_lower or
            'people energize me' in text_lower or 'deep, authentic connections' in text_lower or
            'life is meant to be felt' in text_lower):
            counts['E'] += 3
            counts['N'] += 3
            counts['F'] += 3
            counts['P'] += 3

        # Special check for ISTJ indicators in text
        if ('take responsibility seriously' in text_lower or 'do what\'s right' in text_lower or
            'structure' in text_lower and 'facts' in text_lower or 'reliability' in text_lower or
            'don\'t need the spotlight' in text_lower or 'just get things done' in text_lower or
            'steady, honest, and true' in text_lower or 'always know where i stand' in text_lower):
            counts['I'] += 5  # Strong boost for introversion
            counts['S'] += 3
            counts['T'] += 3
            counts['J'] += 3

        # Special check for ENFJ indicators in text
        if ('lead with vision' in text_lower or 'lift with love' in text_lower or
            'believes in you' in text_lower or 'shine brighter' in text_lower or
            'together we become' in text_lower or 'more than we imagined' in text_lower):
            counts['E'] += 3
            counts['N'] += 3
            counts['F'] += 5  # Strong boost for Feeling over Thinking
            counts['J'] += 3

        # For I/E dimension, give extra weight to introspection and reflection
        if any(word in tokens for word in ['quiet', 'reflection', 'reflect', 'inner', 'internal',
                                          'deep', 'depth', 'introspective', 'introspection']):
            counts['I'] += 2

        # Special case detection for specific MBTI types based on their unique characteristics

        # INFP indicators
        infp_indicators = ['meaning', 'deeply', 'feel', 'feeling', 'feelings', 'value', 'values',
                          'inner', 'truth', 'quiet', 'imagination', 'kind', 'kindness',
                          'authentic', 'authenticity', 'compass']

        # INTJ indicators
        intj_indicators = ['plan', 'strategy', 'strategic', 'logic', 'logical', 'independence',
                          'independent', 'improve', 'judgment', 'vision', 'big picture', 'system']

        # INTP indicators
        intp_indicators = ['curious', 'curiosity', 'why', 'theory', 'theories', 'ideas', 'thoughts',
                          'thinking', 'solve', 'problem', 'distant', 'space', 'unsolvable']

        # ENTJ indicators
        entj_indicators = ['goal', 'goals', 'determination', 'lead', 'leading', 'efficiency',
                          'strategic', 'plan', 'organize', 'driven', 'challenge', 'challenges',
                          'leadership', 'executive', 'command', 'direct', 'structure', 'vision']

        # ENTP indicators
        entp_indicators = ['possibilities', 'argument', 'arguments', 'thinking', 'rules',
                          'explore', 'beyond', 'puzzle', 'shake', 'playful', 'debate']

        # INFJ indicators
        infj_indicators = ['deeply', 'dream', 'better world', 'complex', 'intentions', 'pure',
                          'silence', 'stand', 'bigger', 'heart', 'hearts', 'see into']

        # ENFJ indicators
        enfj_indicators = ['care', 'deeply', 'encourage', 'encouraging', 'others',
                          'together', 'change', 'lead', 'organize', 'harmony', 'inspire',
                          'motivate', 'mentor', 'guide others', 'responsibility', 'structure',
                          'leadership', 'empowering', 'teaching', 'coaching', 'vision', 'love',
                          'believes in you', 'shine', 'brighter', 'lift', 'become more', 'imagined']

        # ENFP indicators
        enfp_indicators = ['magic', 'joy', 'heart', 'messy', 'dream', 'dreams', 'adventure',
                          'connection', 'meaning', 'guide', 'exploring', 'new ideas', 'possibilities',
                          'potential', 'authentic', 'felt', 'feeling', 'follow my heart', 'energize',
                          'thrive', 'deep connections', 'path is messy', 'spontaneous', 'free-spirited']

        # ISTJ indicators
        istj_indicators = ['done', 'right way', 'facts', 'duty', 'principles', 'dependable',
                          'consistency', 'quiet strength', 'responsibility', 'structure', 'reliable',
                          'steady', 'honest', 'true', 'right', 'get things done', 'seriously',
                          'don\'t need the spotlight', 'where I stand', 'keep things steady']

        # ISFJ indicators
        isfj_indicators = ['notice', 'little things', 'give', 'quietly', 'love', 'deeply', 'serve',
                          'traditions', 'loyalty', 'kindness']

        # ESTJ indicators
        estj_indicators = ['order', 'structure', 'results', 'confidence', 'expect', 'work',
                          'responsibility', 'purpose', 'direct', 'leadership', 'efficiency',
                          'organize', 'lead', 'manage', 'decisive', 'assertive', 'take charge',
                          'outspoken', 'straightforward', 'practical solutions', 'spotlight']

        # ESFJ indicators
        esfj_indicators = ['joy', 'helping', 'connecting', 'happy', 'tradition', 'community',
                          'care', 'warmth', 'protect', 'love']

        # ISTP indicators
        istp_indicators = ['moment', 'solve', 'hands-on', 'freedom', 'skill', 'personal space',
                          'rules', 'tools', 'fix', 'build', 'learn', 'doing']

        # ISFP indicators
        isfp_indicators = ['rhythm', 'heart', 'express', 'beauty', 'quiet', 'actions', 'freedom',
                          'authenticity', 'meaning', 'simple', 'soulful', 'moments']

        # ESTP indicators
        estp_indicators = ['action', 'risk', 'results', 'fast', 'feet', 'game', 'win', 'boldness',
                          'charm', 'tools', 'spot', 'jumping', 'energetic', 'moving', 'fun',
                          'real-world', 'figuring out', 'on the spot', 'take risks', 'rules don\'t bother']

        # ESFP indicators
        esfp_indicators = ['energy', 'laughter', 'fun', 'people', 'feel', 'deeply', 'share',
                          'freely', 'day', 'chance', 'shine']

        # Calculate scores for each type
        type_scores = {
            'INFP': sum(2 for word in tokens if word in infp_indicators),
            'INTJ': sum(2 for word in tokens if word in intj_indicators),
            'INTP': sum(2 for word in tokens if word in intp_indicators),
            'ENTJ': sum(2 for word in tokens if word in entj_indicators),
            'ENTP': sum(2 for word in tokens if word in entp_indicators),
            'INFJ': sum(2 for word in tokens if word in infj_indicators),
            'ENFJ': sum(2 for word in tokens if word in enfj_indicators),
            'ENFP': sum(2 for word in tokens if word in enfp_indicators),
            'ISTJ': sum(2 for word in tokens if word in istj_indicators),
            'ISFJ': sum(2 for word in tokens if word in isfj_indicators),
            'ESTJ': sum(2 for word in tokens if word in estj_indicators),
            'ESFJ': sum(2 for word in tokens if word in esfj_indicators),
            'ISTP': sum(2 for word in tokens if word in istp_indicators),
            'ISFP': sum(2 for word in tokens if word in isfp_indicators),
            'ESTP': sum(2 for word in tokens if word in estp_indicators),
            'ESFP': sum(2 for word in tokens if word in esfp_indicators)
        }

        # Check for specific phrases that strongly indicate certain types
        type_phrases = {
            'INFP': ['value inner truth', 'imagination is loud', 'kindness and authenticity', 'feel deeply'],
            'INTJ': ['plan steps ahead', 'value logic', 'improve the world', 'big picture'],
            'INTP': ['curious about everything', 'asking why', 'live in my thoughts', 'solve the unsolvable'],
            'ENTJ': ['set goals', 'fierce determination', 'efficiency and results', 'bold and driven'],
            'ENTP': ['new ideas', 'playful arguments', 'rules are suggestions', 'shake things up'],
            'INFJ': ['feel deeply', 'see into people', 'dream of a better world', 'stand for something bigger'],
            'ENFJ': ['care deeply', 'lead with my heart', 'see potential', 'create real change',
                   'lead with vision', 'lift with love', 'shine brighter', 'believes in you',
                   'together we become more', 'more than we imagined'],
            'ENFP': ['see magic', 'follow my heart', 'chase dreams', 'adventure and connection',
                   'potential everywhere', 'exploring new ideas', 'path is messy', 'people energize me',
                   'deep, authentic connections', 'life is meant to be felt'],
            'ISTJ': ['what needs to be done', 'rely on facts', 'always dependable', 'quiet strength',
                   'take responsibility seriously', 'do what\'s right', 'structure and facts',
                   'reliability keep things steady', 'don\'t need the spotlight', 'just get things done',
                   'steady, honest, and true', 'always know where I stand'],
            'ISFJ': ['notice the little things', 'give quietly', 'traditions with care', 'loyalty and kindness'],
            'ESTJ': ['order and structure', 'lead with confidence', 'work to be done', 'responsibility'],
            'ESFJ': ['joy in helping', 'thrive when others are happy', 'tradition and community', 'warmth and love'],
            'ISTP': ['live in the moment', 'solve problems hands-on', 'rules are tools', 'learn through doing'],
            'ISFP': ['rhythm of my heart', 'express through beauty', 'freedom and authenticity', 'soulful moments'],
            'ESTP': ['love action', 'move fast', 'life is a game', 'boldness and charm',
                   'jumping into action', 'figuring things out on the spot', 'not afraid to take risks',
                   'rules don\'t bother me', 'prefer real-world results', 'moving, solving, and winning'],
            'ESFP': ['light up rooms', 'live for now', 'feel deeply and share', 'new chance to shine']
        }

        # Check for phrases
        for mbti_type, phrases in type_phrases.items():
            for phrase in phrases:
                if phrase in text_lower:
                    type_scores[mbti_type] += 5  # Strong boost for exact phrases

        # Find the type with the highest score
        max_score = 0
        best_type = None

        for mbti_type, score in type_scores.items():
            if score > max_score:
                max_score = score
                best_type = mbti_type

        # If we have a clear winner with a significant score, use it
        if best_type and max_score >= 4:
            return best_type

        # Otherwise, fall back to the traditional method
        mbti = ''
        mbti += 'E' if counts['E'] > counts['I'] else 'I'
        mbti += 'S' if counts['S'] > counts['N'] else 'N'
        mbti += 'T' if counts['T'] > counts['F'] else 'F'
        mbti += 'J' if counts['J'] > counts['P'] else 'P'

        return mbti

    def suggest_careers(self, big_five, mbti):
        """
        Suggest careers based on personality traits.

        Args:
            big_five (list): Big Five traits [EXT, NEU, AGR, CON, OPN]
            mbti (str): MBTI type

        Returns:
            list: Suggested careers
        """
        # Career suggestions based on MBTI type
        mbti_careers = {
            'INTJ': ['Strategic Planner', 'Systems Analyst', 'Scientific Researcher', 'Engineer', 'Investment Banker'],
            'INTP': ['Software Developer', 'Data Scientist', 'Architect', 'Professor', 'Research Scientist'],
            'ENTJ': ['Executive', 'Management Consultant', 'Lawyer', 'Entrepreneur', 'Business Analyst'],
            'ENTP': ['Entrepreneur', 'Creative Director', 'Marketing Strategist', 'Consultant', 'Product Manager'],
            'INFJ': ['Counselor', 'HR Development Trainer', 'Writer', 'Psychologist', 'Social Worker'],
            'INFP': ['Writer', 'Graphic Designer', 'Psychologist', 'Teacher', 'Artist'],
            'ENFJ': ['Training Manager', 'Public Relations Specialist', 'Sales Manager', 'HR Manager', 'Teacher'],
            'ENFP': ['Journalist', 'Marketing Manager', 'Event Planner', 'Public Relations', 'Counselor'],
            'ISTJ': ['Accountant', 'Project Manager', 'Financial Analyst', 'Auditor', 'Operations Manager'],
            'ISFJ': ['Nurse', 'Administrative Assistant', 'Elementary Teacher', 'Social Worker', 'HR Specialist'],
            'ESTJ': ['Sales Manager', 'Project Manager', 'Financial Manager', 'Military Officer', 'Judge'],
            'ESFJ': ['Nurse', 'Teacher', 'Sales Representative', 'Event Planner', 'HR Specialist'],
            'ISTP': ['Engineer', 'Mechanic', 'Pilot', 'Forensic Scientist', 'Software Developer'],
            'ISFP': ['Graphic Designer', 'Fashion Designer', 'Photographer', 'Chef', 'Veterinarian'],
            'ESTP': ['Sales Representative', 'Marketing Executive', 'Entrepreneur', 'Paramedic', 'Detective'],
            'ESFP': ['Event Planner', 'Sales Representative', 'Tour Guide', 'Performer', 'Public Relations']
        }

        # Get career suggestions based on MBTI
        mbti_suggestions = mbti_careers.get(mbti, [])

        # Additional suggestions based on Big Five traits
        big_five_suggestions = []

        # Extraversion (EXT)
        if big_five[0] == 1:  # High
            big_five_suggestions.extend(['Sales Manager', 'Public Relations', 'Event Coordinator'])
        else:  # Low
            big_five_suggestions.extend(['Research Scientist', 'Software Developer', 'Content Writer'])

        # Openness (OPN)
        if big_five[4] == 1:  # High
            big_five_suggestions.extend(['Creative Director', 'UX Designer', 'Innovation Consultant'])
        else:  # Low
            big_five_suggestions.extend(['Accountant', 'Operations Manager', 'Quality Assurance'])

        # Combine and remove duplicates
        all_suggestions = list(set(mbti_suggestions + big_five_suggestions))

        # Return top suggestions (up to 5)
        return all_suggestions[:5]

    def analyze_communication_style(self, big_five, mbti):
        """
        Analyze communication style based on personality traits.

        Args:
            big_five (list): Big Five traits [EXT, NEU, AGR, CON, OPN]
            mbti (str): MBTI type

        Returns:
            dict: Communication style insights
        """
        EXT, NEU, AGR, CON, OPN = big_five

        style = {
            'primary_style': '',
            'strengths': [],
            'challenges': [],
            'tips': []
        }

        # Determine primary communication style with neuroticism influence
        if EXT == 1 and AGR == 1:
            if NEU == 0:
                style['primary_style'] = 'Confident and Collaborative'
            else:
                style['primary_style'] = 'Expressive and Relationship-Focused'
        elif EXT == 1 and AGR == 0:
            if NEU == 0:
                style['primary_style'] = 'Direct and Assertive'
            else:
                style['primary_style'] = 'Passionate and Challenging'
        elif EXT == 0 and AGR == 1:
            if NEU == 0:
                style['primary_style'] = 'Thoughtful and Supportive'
            else:
                style['primary_style'] = 'Careful and Considerate'
        else:  # EXT == 0 and AGR == 0
            if NEU == 0:
                style['primary_style'] = 'Analytical and Reserved'
            else:
                style['primary_style'] = 'Precise and Cautious'

        # Add strengths based on traits
        if EXT == 1:
            style['strengths'].append('Engaging others in conversation')
        else:
            style['strengths'].append('Listening attentively')

        if AGR == 1:
            style['strengths'].append('Creating harmony in discussions')
        else:
            style['strengths'].append('Providing honest, critical feedback')

        if CON == 1:
            style['strengths'].append('Communicating in an organized, structured way')

        if OPN == 1:
            style['strengths'].append('Bringing creative ideas to discussions')

        # Add neuroticism-related strengths
        if NEU == 0:
            style['strengths'].append('Maintaining composure during difficult conversations')
        else:
            style['strengths'].append('Showing emotional awareness and sensitivity')

        # Add challenges
        if EXT == 0:
            style['challenges'].append('May struggle to speak up in group settings')
        else:
            style['challenges'].append('May dominate conversations')

        if AGR == 0:
            style['challenges'].append('May come across as too critical or blunt')
        else:
            style['challenges'].append('May avoid necessary confrontation')

        # Add neuroticism-related challenges
        if NEU == 1:
            style['challenges'].append('May become stressed during conflict or criticism')

        # Add tips
        if 'N' in mbti and 'T' in mbti:
            style['tips'].append('Focus on providing context before diving into details')

        if 'S' in mbti and 'F' in mbti:
            style['tips'].append('Include personal elements in your communication')

        if EXT == 0:
            style['tips'].append('Prepare talking points in advance for meetings')

        if AGR == 0:
            style['tips'].append('Balance critique with positive feedback')

        # Add neuroticism-related tips
        if NEU == 1:
            style['tips'].append('Practice stress management techniques before difficult conversations')

        return style

    def analyze_leadership_style(self, big_five, mbti):
        """
        Analyze leadership style based on personality traits.

        Args:
            big_five (list): Big Five traits [EXT, NEU, AGR, CON, OPN]
            mbti (str): MBTI type

        Returns:
            dict: Leadership style insights
        """
        EXT, NEU, AGR, CON, OPN = big_five

        style = {
            'primary_style': '',
            'strengths': [],
            'challenges': [],
            'tips': []
        }

        # Determine primary leadership style with neuroticism influence
        if CON == 1 and AGR == 0:
            if NEU == 0:
                style['primary_style'] = 'Decisive and Directive Leader'
            else:
                style['primary_style'] = 'Detail-Oriented and Exacting Leader'
        elif CON == 1 and AGR == 1:
            if NEU == 0:
                style['primary_style'] = 'Structured and Supportive Leader'
            else:
                style['primary_style'] = 'Conscientious and Considerate Leader'
        elif CON == 0 and OPN == 1:
            if NEU == 0:
                style['primary_style'] = 'Visionary and Innovative Leader'
            else:
                style['primary_style'] = 'Creative and Thoughtful Leader'
        elif EXT == 1 and AGR == 1:
            if NEU == 0:
                style['primary_style'] = 'Confident and Inspirational Leader'
            else:
                style['primary_style'] = 'Enthusiastic and Empathetic Leader'
        else:
            if NEU == 0:
                style['primary_style'] = 'Adaptable and Resilient Leader'
            else:
                style['primary_style'] = 'Reflective and Responsive Leader'

        # Add strengths based on traits
        if CON == 1:
            style['strengths'].append('Creating clear processes and expectations')

        if OPN == 1:
            style['strengths'].append('Encouraging innovation and creative solutions')

        if AGR == 1:
            style['strengths'].append('Building team cohesion and morale')
        else:
            style['strengths'].append('Making tough decisions without hesitation')

        if EXT == 1:
            style['strengths'].append('Energizing and motivating team members')

        # Add neuroticism-related strengths
        if NEU == 0:
            style['strengths'].append('Staying calm under pressure and managing stress effectively')
        else:
            style['strengths'].append('Anticipating potential problems and planning contingencies')

        # Add challenges
        if CON == 1 and OPN == 0:
            style['challenges'].append('May resist change or new approaches')

        if AGR == 1 and CON == 0:
            style['challenges'].append('May struggle with enforcing standards')

        if EXT == 0:
            style['challenges'].append('May need to make extra effort to be visible to the team')

        # Add neuroticism-related challenges
        if NEU == 1:
            style['challenges'].append('May become overwhelmed during crises or high-pressure situations')
            style['challenges'].append('May struggle with delegating due to perfectionism')

        # Add tips based on MBTI
        if 'J' in mbti:
            style['tips'].append('Allow flexibility in how team members accomplish goals')
        else:
            style['tips'].append('Provide clear deadlines and expectations')

        if 'T' in mbti:
            style['tips'].append('Remember to acknowledge emotional aspects of workplace situations')
        else:
            style['tips'].append('Balance empathy with objective decision-making')

        # Add neuroticism-related tips
        if NEU == 1:
            style['tips'].append('Develop stress management techniques for high-pressure situations')
            style['tips'].append('Practice delegating tasks to trusted team members')
        else:
            style['tips'].append('Be attentive to team members who may be experiencing stress')

        return style
