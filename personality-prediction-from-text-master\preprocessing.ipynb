{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Preprocessing of data\n"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import essay\n", "import numpy as np\n", "import pickle\n", "import re"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def mbti_to_big5(mbti):\n", "    # check https://en.wikipedia.org/wiki/Myers%E2%80%93Briggs_Type_Indicator\n", "    # in mbti (my<PERSON> briggs) ther is invrovert vs. extrovert\n", "    # which corellates with Extroversion in BIG FIVE\n", "    mbti = mbti.lower()\n", "    cEXT, cNEU, cAGR, cCON, cOPN = 0,np.NaN,0,0,0\n", "    if mbti[0] == \"i\":\n", "        cEXT = 0\n", "    elif mbti[0] == \"e\":\n", "        cEXT = 1\n", "\n", "    # in mbti (my<PERSON> briggs) ther is I*N*TUITION vs SENSING\n", "    # which corellates with OPENNESS in BIG FIVE\n", "    if mbti[1] == \"n\":\n", "        cOPN = 1\n", "    elif mbti[1] == \"s\":\n", "        cOPN = 0   \n", "\n", "    # in mbti (my<PERSON> briggs) ther is THINKER vs FEELER\n", "    # which corellates with AGREEABLENESS in BIG FIVE\n", "    if mbti[2] == \"t\":\n", "        cAGR = 0\n", "    elif mbti[2] == \"f\":\n", "        cAGR = 1\n", "\n", "    # in mbti (my<PERSON> briggs) ther is JUDGER vs PERCEIVER\n", "    # which corellates with CONSCIENTIOUSNESS in BIG FIVE (worst corellation)\n", "    # especially bec. orderlyness corellates with conscientiousness\n", "    if mbti[3] == \"p\":\n", "        cCON = 0\n", "    elif mbti[3] == \"j\":\n", "        cCON = 1\n", "\n", "    return cEXT, cNEU, cAGR, cCON, cOPN"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def remove_unemotional_scentences(emotional_words, text_as_one_string):\n", "    reduced_s = \"\"\n", "    scentences = re.split('(?<=[.!?]) +', text_as_one_string)\n", "    for s in scentences:\n", "        if any(e in s for e in emotional_words):\n", "            reduced_s = reduced_s + s + \" \"\n", "        else:\n", "            pass\n", "    return reduced_s"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["# simply put every row of our read dataframe into a list of \n", "# the object \"Essay\"\n", "# remove data from list substract\n", "def create_essays(df, subtract=None):\n", "    essays = []\n", "    for index, row in df.iterrows():\n", "        essays.append(essay.<PERSON><PERSON>y(row.TEXT, row.cEXT, row.cNEU, row.cAGR, row.cCON, row.cOPN))  \n", "\n", "    # remove scentences which do not contain emotionally charged words \n", "    # from the emotional lexicon\n", "    if subtract != None:\n", "        for x in essays:\n", "            x.filtered_text = remove_unemotional_scentences(emotional_words, x.clean_text)\n", "\n", "    return essays"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Loading the essays from paper\n", "## scientific gold standard\n", "## https://sentic.net/deep-learning-based-personality-detection.pdf\n", "### (scientific gold standard \"stream of counsciousness\" essays labeled with personality traits of the big five)"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>TEXT</th>\n", "      <th>cEXT</th>\n", "      <th>cNEU</th>\n", "      <th>cAGR</th>\n", "      <th>cCON</th>\n", "      <th>cOPN</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Well, right now I just woke up from a mid-day ...</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Well, here we go with the stream of consciousn...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>An open keyboard and buttons to push. The thin...</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>I can't believe it!  It's really happening!  M...</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>Well, here I go with the good old stream of co...</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2462</th>\n", "      <td>I'm home. wanted to go to bed but remembe...</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2463</th>\n", "      <td>Stream of consiousnesssskdj. How do you s...</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2464</th>\n", "      <td>It is Wednesday, December 8th and a lot has be...</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2465</th>\n", "      <td>Man this week has been hellish. Anyways, now i...</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2466</th>\n", "      <td>I have just gotten off the phone with brady. I...</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>0</td>\n", "      <td>1</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2467 rows × 6 columns</p>\n", "</div>"], "text/plain": ["                                                   TEXT  cEXT  cNEU  cAGR  \\\n", "0     Well, right now I just woke up from a mid-day ...     0     1     1   \n", "1     Well, here we go with the stream of consciousn...     0     0     1   \n", "2     An open keyboard and buttons to push. The thin...     0     1     0   \n", "3     I can't believe it!  It's really happening!  M...     1     0     1   \n", "4     Well, here I go with the good old stream of co...     1     0     1   \n", "...                                                 ...   ...   ...   ...   \n", "2462       I'm home. wanted to go to bed but remembe...     0     1     0   \n", "2463       Stream of consiousnesssskdj. How do you s...     1     1     0   \n", "2464  It is Wednesday, December 8th and a lot has be...     0     0     1   \n", "2465  Man this week has been hellish. Anyways, now i...     0     1     0   \n", "2466  I have just gotten off the phone with brady. I...     0     1     1   \n", "\n", "      cCON  cOPN  \n", "0        0     1  \n", "1        0     0  \n", "2        1     1  \n", "3        1     0  \n", "4        0     1  \n", "...    ...   ...  \n", "2462     1     0  \n", "2463     0     1  \n", "2464     0     0  \n", "2465     0     1  \n", "2466     0     1  \n", "\n", "[2467 rows x 6 columns]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["# we read in the data from \"essays.csv\" and \n", "# \"essays.csv\" contains all essays with classification \n", "df_essays = pd.read_csv('data/training/essays.csv', encoding='cp1252', delimiter=',', quotechar='\"')\n", "\n", "# for every essay, we replace the personalitiy categories \n", "# of the essay wich are \"y\" and \"n\" with \"1\" and \"0\" \n", "for e in df_essays.columns[2:7]:\n", "    df_essays[e] = df_essays[e].replace('n', '0')\n", "    df_essays[e] = df_essays[e].replace('y', '1')\n", "    # not sure if we need this line: furter investigation possible:\n", "    df_essays[e] = pd.to_numeric(df_essays[e])\n", "\n", "df_essays = df_essays[[\"TEXT\", \"cEXT\", \"cNEU\", \"cAGR\", \"cCON\", \"cOPN\"]]\n", "df_essays"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Load the MBTI kaggle dataset\n", "## https://www.kaggle.com/datasnaek/mbti-type"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"ename": "AttributeError", "evalue": "`np.NaN` was removed in the NumPy 2.0 release. Use `np.nan` instead.", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mAttributeError\u001b[39m                            <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[6]\u001b[39m\u001b[32m, line 3\u001b[39m\n\u001b[32m      1\u001b[39m df_kaggle = pd.read_csv(\u001b[33m'\u001b[39m\u001b[33mdata/training/mbti_1.csv\u001b[39m\u001b[33m'\u001b[39m,  skiprows=\u001b[32m0\u001b[39m )\n\u001b[32m----> \u001b[39m\u001b[32m3\u001b[39m df_kaggle[\u001b[33m\"\u001b[39m\u001b[33mcEXT\u001b[39m\u001b[33m\"\u001b[39m] =   \u001b[43mdf_kaggle\u001b[49m\u001b[43m.\u001b[49m\u001b[43mapply\u001b[49m\u001b[43m(\u001b[49m\u001b[38;5;28;43;01mlambda\u001b[39;49;00m\u001b[43m \u001b[49m\u001b[43mx\u001b[49m\u001b[43m:\u001b[49m\u001b[43m \u001b[49m\u001b[43mmbti_to_big5\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[43m.\u001b[49m\u001b[43mtype\u001b[49m\u001b[43m)\u001b[49m\u001b[43m[\u001b[49m\u001b[32;43m0\u001b[39;49m\u001b[43m]\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[32;43m1\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m      4\u001b[39m df_kaggle[\u001b[33m\"\u001b[39m\u001b[33mcNEU\u001b[39m\u001b[33m\"\u001b[39m] =   df_kaggle.apply(\u001b[38;5;28;01mlambda\u001b[39;00m x: mbti_to_big5(x.type)[\u001b[32m1\u001b[39m], \u001b[32m1\u001b[39m)\n\u001b[32m      5\u001b[39m df_kaggle[\u001b[33m\"\u001b[39m\u001b[33mcAGR\u001b[39m\u001b[33m\"\u001b[39m] =   df_kaggle.apply(\u001b[38;5;28;01mlambda\u001b[39;00m x: mbti_to_big5(x.type)[\u001b[32m2\u001b[39m], \u001b[32m1\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Downloads/personality-prediction-from-text-master/venv/lib/python3.12/site-packages/pandas/core/frame.py:10374\u001b[39m, in \u001b[36mDataFrame.apply\u001b[39m\u001b[34m(self, func, axis, raw, result_type, args, by_row, engine, engine_kwargs, **kwargs)\u001b[39m\n\u001b[32m  10360\u001b[39m \u001b[38;5;28;01mfrom\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mcore\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mapply\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mimport\u001b[39;00m frame_apply\n\u001b[32m  10362\u001b[39m op = frame_apply(\n\u001b[32m  10363\u001b[39m     \u001b[38;5;28mself\u001b[39m,\n\u001b[32m  10364\u001b[39m     func=func,\n\u001b[32m   (...)\u001b[39m\u001b[32m  10372\u001b[39m     kwargs=kwargs,\n\u001b[32m  10373\u001b[39m )\n\u001b[32m> \u001b[39m\u001b[32m10374\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43mop\u001b[49m\u001b[43m.\u001b[49m\u001b[43mapply\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m.__finalize__(\u001b[38;5;28mself\u001b[39m, method=\u001b[33m\"\u001b[39m\u001b[33mapply\u001b[39m\u001b[33m\"\u001b[39m)\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Downloads/personality-prediction-from-text-master/venv/lib/python3.12/site-packages/pandas/core/apply.py:916\u001b[39m, in \u001b[36mFrameApply.apply\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m    913\u001b[39m \u001b[38;5;28;01mel<PERSON>\u001b[39;00m \u001b[38;5;28mself\u001b[39m.raw:\n\u001b[32m    914\u001b[39m     \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28mself\u001b[39m.apply_raw(engine=\u001b[38;5;28mself\u001b[39m.engine, engine_kwargs=\u001b[38;5;28mself\u001b[39m.engine_kwargs)\n\u001b[32m--> \u001b[39m\u001b[32m916\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mapply_standard\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Downloads/personality-prediction-from-text-master/venv/lib/python3.12/site-packages/pandas/core/apply.py:1063\u001b[39m, in \u001b[36mFrameApply.apply_standard\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m   1061\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mapply_standard\u001b[39m(\u001b[38;5;28mself\u001b[39m):\n\u001b[32m   1062\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28mself\u001b[39m.engine == \u001b[33m\"\u001b[39m\u001b[33mpython\u001b[39m\u001b[33m\"\u001b[39m:\n\u001b[32m-> \u001b[39m\u001b[32m1063\u001b[39m         results, res_index = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mapply_series_generator\u001b[49m\u001b[43m(\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1064\u001b[39m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m   1065\u001b[39m         results, res_index = \u001b[38;5;28mself\u001b[39m.apply_series_numba()\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Downloads/personality-prediction-from-text-master/venv/lib/python3.12/site-packages/pandas/core/apply.py:1081\u001b[39m, in \u001b[36mFrameApply.apply_series_generator\u001b[39m\u001b[34m(self)\u001b[39m\n\u001b[32m   1078\u001b[39m \u001b[38;5;28;01mwith\u001b[39;00m option_context(\u001b[33m\"\u001b[39m\u001b[33mmode.chained_assignment\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m):\n\u001b[32m   1079\u001b[39m     \u001b[38;5;28;01mfor\u001b[39;00m i, v \u001b[38;5;129;01min\u001b[39;00m \u001b[38;5;28menumerate\u001b[39m(series_gen):\n\u001b[32m   1080\u001b[39m         \u001b[38;5;66;03m# ignore SettingWithCopy here in case the user mutates\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m1081\u001b[39m         results[i] = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mfunc\u001b[49m\u001b[43m(\u001b[49m\u001b[43mv\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43margs\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mkwargs\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1082\u001b[39m         \u001b[38;5;28;01mif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(results[i], ABCSeries):\n\u001b[32m   1083\u001b[39m             \u001b[38;5;66;03m# If we have a view on v, we need to make a copy because\u001b[39;00m\n\u001b[32m   1084\u001b[39m             \u001b[38;5;66;03m#  series_generator will swap out the underlying data\u001b[39;00m\n\u001b[32m   1085\u001b[39m             results[i] = results[i].copy(deep=\u001b[38;5;28;01mFalse\u001b[39;00m)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[6]\u001b[39m\u001b[32m, line 3\u001b[39m, in \u001b[36m<lambda>\u001b[39m\u001b[34m(x)\u001b[39m\n\u001b[32m      1\u001b[39m df_kaggle = pd.read_csv(\u001b[33m'\u001b[39m\u001b[33mdata/training/mbti_1.csv\u001b[39m\u001b[33m'\u001b[39m,  skiprows=\u001b[32m0\u001b[39m )\n\u001b[32m----> \u001b[39m\u001b[32m3\u001b[39m df_kaggle[\u001b[33m\"\u001b[39m\u001b[33mcEXT\u001b[39m\u001b[33m\"\u001b[39m] =   df_kaggle.apply(\u001b[38;5;28;01mlambda\u001b[39;00m x: \u001b[43mmbti_to_big5\u001b[49m\u001b[43m(\u001b[49m\u001b[43mx\u001b[49m\u001b[43m.\u001b[49m\u001b[43mtype\u001b[49m\u001b[43m)\u001b[49m[\u001b[32m0\u001b[39m], \u001b[32m1\u001b[39m)\n\u001b[32m      4\u001b[39m df_kaggle[\u001b[33m\"\u001b[39m\u001b[33mcNEU\u001b[39m\u001b[33m\"\u001b[39m] =   df_kaggle.apply(\u001b[38;5;28;01mlambda\u001b[39;00m x: mbti_to_big5(x.type)[\u001b[32m1\u001b[39m], \u001b[32m1\u001b[39m)\n\u001b[32m      5\u001b[39m df_kaggle[\u001b[33m\"\u001b[39m\u001b[33mcAGR\u001b[39m\u001b[33m\"\u001b[39m] =   df_kaggle.apply(\u001b[38;5;28;01mlambda\u001b[39;00m x: mbti_to_big5(x.type)[\u001b[32m2\u001b[39m], \u001b[32m1\u001b[39m)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 6\u001b[39m, in \u001b[36mmbti_to_big5\u001b[39m\u001b[34m(mbti)\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mmbti_to_big5\u001b[39m(mbti):\n\u001b[32m      2\u001b[39m     \u001b[38;5;66;03m# check https://en.wikipedia.org/wiki/Myers%E2%80%93Briggs_Type_Indicator\u001b[39;00m\n\u001b[32m      3\u001b[39m     \u001b[38;5;66;03m# in mbti (my<PERSON> briggs) ther is invrovert vs. extrovert\u001b[39;00m\n\u001b[32m      4\u001b[39m     \u001b[38;5;66;03m# which corellates with Extroversion in BIG FIVE\u001b[39;00m\n\u001b[32m      5\u001b[39m     mbti = mbti.lower()\n\u001b[32m----> \u001b[39m\u001b[32m6\u001b[39m     cEXT, cNEU, cAGR, cCON, cOPN = \u001b[32m0\u001b[39m,\u001b[43mnp\u001b[49m\u001b[43m.\u001b[49m\u001b[43mNaN\u001b[49m,\u001b[32m0\u001b[39m,\u001b[32m0\u001b[39m,\u001b[32m0\u001b[39m\n\u001b[32m      7\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m mbti[\u001b[32m0\u001b[39m] == \u001b[33m\"\u001b[39m\u001b[33mi\u001b[39m\u001b[33m\"\u001b[39m:\n\u001b[32m      8\u001b[39m         cEXT = \u001b[32m0\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32m~/Downloads/personality-prediction-from-text-master/venv/lib/python3.12/site-packages/numpy/__init__.py:400\u001b[39m, in \u001b[36m__getattr__\u001b[39m\u001b[34m(attr)\u001b[39m\n\u001b[32m    397\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mAttributeError\u001b[39;00m(__former_attrs__[attr], name=\u001b[38;5;28;01mNone\u001b[39;00m)\n\u001b[32m    399\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m attr \u001b[38;5;129;01min\u001b[39;00m __expired_attributes__:\n\u001b[32m--> \u001b[39m\u001b[32m400\u001b[39m     \u001b[38;5;28;01mraise\u001b[39;00m \u001b[38;5;167;01mAttributeError\u001b[39;00m(\n\u001b[32m    401\u001b[39m         \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[33m`np.\u001b[39m\u001b[38;5;132;01m{\u001b[39;00mattr\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m` was removed in the NumPy 2.0 release. \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    402\u001b[39m         \u001b[33mf\u001b[39m\u001b[33m\"\u001b[39m\u001b[38;5;132;01m{\u001b[39;00m__expired_attributes__[attr]\u001b[38;5;132;01m}\u001b[39;00m\u001b[33m\"\u001b[39m,\n\u001b[32m    403\u001b[39m         name=\u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m    404\u001b[39m     )\n\u001b[32m    406\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m attr == \u001b[33m\"\u001b[39m\u001b[33mchararray\u001b[39m\u001b[33m\"\u001b[39m:\n\u001b[32m    407\u001b[39m     warnings.warn(\n\u001b[32m    408\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33m`np.chararray` is deprecated and will be removed from \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    409\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mthe main namespace in the future. Use an array with a string \u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m    410\u001b[39m         \u001b[33m\"\u001b[39m\u001b[33mor bytes dtype instead.\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;167;01mDeprecationWarning\u001b[39;00m, stacklevel=\u001b[32m2\u001b[39m)\n", "\u001b[31mAttributeError\u001b[39m: `np.NaN` was removed in the NumPy 2.0 release. Use `np.nan` instead."]}], "source": ["df_kaggle = pd.read_csv('data/training/mbti_1.csv',  skiprows=0 )\n", "\n", "df_kaggle[\"cEXT\"] =   df_kaggle.apply(lambda x: mbti_to_big5(x.type)[0], 1)\n", "df_kaggle[\"cNEU\"] =   df_kaggle.apply(lambda x: mbti_to_big5(x.type)[1], 1)\n", "df_kaggle[\"cAGR\"] =   df_kaggle.apply(lambda x: mbti_to_big5(x.type)[2], 1)\n", "df_kaggle[\"cCON\"] =   df_kaggle.apply(lambda x: mbti_to_big5(x.type)[3], 1)\n", "df_kaggle[\"cOPN\"] =   df_kaggle.apply(lambda x: mbti_to_big5(x.type)[4], 1)\n", "\n", "df_kaggle = df_kaggle[[\"posts\", \"cEXT\", \"cNEU\", \"cAGR\", \"cCON\", \"cOPN\"]]\n", "df_kaggle.columns = [\"TEXT\", \"cEXT\", \"cNEU\", \"cAGR\", \"cCON\", \"cOPN\"]\n", "\n", "# remove som fancy ||| things\n", "df_kaggle[\"TEXT\"] = df_kaggle.apply(lambda x: x.TEXT.replace(\"|||\", \" \")[:], 1)\n", "\n", "\n", "df_kaggle"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Load Reddit Dataset\n", "## <PERSON><PERSON>  https://peopleswksh.github.io/pdf/PEOPLES12.pdf\n", "### received on request\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# the file is kinda huge. thanks <PERSON><PERSON>\n", "file = 'data/training/typed_comments.csv'\n", "df_reddit = pd.read_csv(file)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["#remove some rows to to keep longer text (the 420 because it makes avg word count compareable to the rest of the data)\n", "df_reddit = df_reddit[df_reddit.word_count > 420]\n", "\n", "df_reddit[\"cEXT\"] =   df_reddit.apply(lambda x: mbti_to_big5(x.type)[0], 1)\n", "df_reddit[\"cNEU\"] =   df_reddit.apply(lambda x: mbti_to_big5(x.type)[1], 1)\n", "df_reddit[\"cAGR\"] =   df_reddit.apply(lambda x: mbti_to_big5(x.type)[2], 1)\n", "df_reddit[\"cCON\"] =   df_reddit.apply(lambda x: mbti_to_big5(x.type)[3], 1)\n", "df_reddit[\"cOPN\"] =   df_reddit.apply(lambda x: mbti_to_big5(x.type)[4], 1)\n", "df_reddit = df_reddit[[\"comment\", \"cEXT\", \"cNEU\", \"cAGR\", \"cCON\", \"cOPN\"]]\n", "df_reddit.columns = [\"TEXT\", \"cEXT\", \"cNEU\", \"cAGR\", \"cCON\", \"cOPN\"]\n", "df_reddit.reset_index(drop=True, inplace=True)\n", "df_reddit"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Load Emotional Lexicon to substract from data"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# also from \"Emotional_Lexicon.csv\" we read in the data, which is a list of words and \n", "# has several categories of emotions. \n", "# anger - anticipation - disgust - fear - joy - negative - positive \n", "# - sadness - surprise - trust - Charged\n", "df_lexicon = pd.read_csv('data/training/Emotion_Lexicon.csv', index_col=0)\n", "\n", "\n", "# some of the words have no emotional category, \n", "# so let's remove them as they have no use to us.\n", "# can be improved by not even loading them when all columns are 0. maybe later.\n", "df_lexicon = df_lexicon[(df_lexicon.T != 0).any()]\n", "emotional_words = df_lexicon.index.tolist()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Concatinate the datasets for 3 different data to work with and compare to"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create Data Base 1 - only esseys.csv - and save as object list"]}, {"cell_type": "code", "execution_count": null, "metadata": {"scrolled": true}, "outputs": [], "source": ["# save preprocessed data by converting into OBJECT essay and save with pickle and removing non emotional scentences\n", "essays = create_essays(df_essays, emotional_words)\n", "pickle.dump(essays, open( \"essays/essays2467.p\", \"wb\"))\n", "print(\"saved entries: \", len(essays))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create Data Base 2 - Essay Data and Kaggle data - and save as object list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# concatinate the dataframes:\n", "frames  = [df_essays, df_kaggle]\n", "essays_kaggle = pd.concat(frames, sort=False)\n", "essays_kaggle.reset_index(drop=True)\n", "\n", "# preprocess data by converting into OBJECT essay and save with pickle and removing non emotional scentences\n", "essays_kaggle = create_essays(essays_kaggle, emotional_words)\n", "pickle.dump(essays_kaggle, open(\"essays/essays11142.p\", \"wb\"))\n", "print(\"saved entries: \", len(essays_kaggle))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Create Data Base 3 - Essay Data and Kaggle data and Reddit data - and save as object list"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# concatinate the dataframes:\n", "frames  = [df_essays, df_kaggle, df_reddit]\n", "essays_kaggle_reddit = pd.concat(frames, sort=False)\n", "essays_kaggle_reddit.reset_index(drop=True)\n", "\n", "# preprocess data by converting into OBJECT essay and save with pickle and removing non emotional scentences\n", "essays_kaggle_reddit = create_essays(essays_kaggle_reddit, emotional_words)\n", "pickle.dump(essays_kaggle_reddit, open(\"essays/essays89364.p\", \"wb\"))\n", "print(\"saved entries: \", len(essays_kaggle_reddit))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}