#!/usr/bin/env python3
"""
Test script for the improved personality analysis system.
"""

from simple_personality_predictor import SimplePersonalityPredictor

def test_mbti_descriptions():
    """Test the personality predictor with the 16 MBTI type descriptions."""
    
    # Initialize the predictor
    predictor = SimplePersonalityPredictor()
    
    # MBTI type descriptions
    mbti_descriptions = {
        # Analysts
        "INTJ": """I see the big picture and plan steps ahead.
I value logic and independence deeply.
Emotions don't cloud my judgment, but I respect them.
I quietly strive to improve the world through strategy.""",
        
        "INTP": """I'm curious about everything and always asking why.
I live in my thoughts, building ideas and theories.
I may seem distant, but I care in my own way.
Give me space and I'll solve the unsolvable.""",
        
        "ENTJ": """I set goals and chase them with fierce determination.
Leading comes naturally—I see what others miss.
Efficiency and results matter to me.
I'm bold, driven, and not afraid of challenges.""",
        
        "ENTP": """I thrive on new ideas and endless possibilities.
I love playful arguments and sharp thinking.
Rules are suggestions—I explore beyond them.
Life is a puzzle, and I'm here to shake things up.""",
        
        # Diplomats
        "INFJ": """I feel deeply and see into people's hearts.
I dream of a better world and work quietly toward it.
My mind is complex, but my intentions are pure.
Even in silence, I stand for something bigger.""",
        
        "INFP": """I live through meaning, not just moments.
I feel everything deeply and value inner truth.
I may be quiet, but my imagination is loud.
Kindness and authenticity are my compass.""",
        
        "ENFJ": """I care deeply and lead with my heart.
I see potential in everyone I meet.
Encouraging others is my gift.
Together, I believe we can create real change.""",
        
        "ENFP": """I see magic in the ordinary and joy in people.
I follow my heart, even if it's messy.
I chase dreams and bring others along.
Adventure, connection, and meaning guide me.""",
        
        # Sentinels
        "ISTJ": """I do what needs to be done, the right way.
I rely on facts, duty, and clear principles.
I may not say much, but I'm always dependable.
Consistency is my quiet strength.""",
        
        "ISFJ": """I notice the little things others miss.
I give quietly, love deeply, and serve faithfully.
I carry traditions with care and heart.
Loyalty and kindness shape who I am.""",
        
        "ESTJ": """I believe in order, structure, and results.
I lead with confidence and expect the best.
When there's work to be done, I'm all in.
Responsibility isn't a burden—it's my purpose.""",
        
        "ESFJ": """I find joy in helping and connecting.
I thrive when those around me are happy.
Tradition, community, and care guide me.
I lead with warmth and protect with love.""",
        
        # Explorers
        "ISTP": """I live in the moment and solve problems hands-on.
I value freedom, skill, and personal space.
Rules are tools—not limits.
I fix, build, and learn through doing.""",
        
        "ISFP": """I move to the rhythm of my heart.
I express myself through beauty and quiet actions.
Freedom and authenticity matter most.
I find meaning in simple, soulful moments.""",
        
        "ESTP": """I love action, risk, and real-world results.
I move fast and think on my feet.
Life is a game, and I play to win.
Boldness and charm are my tools.""",
        
        "ESFP": """I light up rooms with my energy and laughter.
I live for now, for fun, and for people.
I feel deeply and share freely.
Every day is a new chance to shine."""
    }
    
    # Test each description
    print("Testing MBTI Type Descriptions:")
    print("=" * 50)
    
    correct_count = 0
    for mbti_type, description in mbti_descriptions.items():
        # Predict MBTI type
        predicted_type = predictor.predict_mbti(description)
        
        # Check if prediction is correct
        is_correct = predicted_type == mbti_type
        if is_correct:
            correct_count += 1
            
        # Print results
        print(f"Type: {mbti_type}")
        print(f"Predicted: {predicted_type}")
        print(f"Correct: {'✓' if is_correct else '✗'}")
        
        # Get Big Five traits
        big_five = predictor.predict_big_five(description)
        traits = ["Extraversion", "Neuroticism", "Agreeableness", "Conscientiousness", "Openness"]
        print("Big Five Traits:")
        for i, trait in enumerate(traits):
            value = "High" if big_five[i] == 1 else "Low"
            print(f"  {trait}: {value}")
            
        print("-" * 50)
    
    # Print overall accuracy
    accuracy = correct_count / len(mbti_descriptions) * 100
    print(f"Overall Accuracy: {accuracy:.2f}%")
    
    # Test the specific INFP case from the screenshot
    infp_text = """I live through meaning, not just moments.
I feel everything deeply and value inner truth.
I may be quiet, but my imagination is loud.
Kindness and authenticity are my compass."""
    
    print("\nSpecial Test Case (INFP):")
    print("=" * 50)
    
    # Predict MBTI type
    predicted_type = predictor.predict_mbti(infp_text)
    
    # Print results
    print(f"Expected: INFP")
    print(f"Predicted: {predicted_type}")
    print(f"Correct: {'✓' if predicted_type == 'INFP' else '✗'}")
    
    # Get Big Five traits
    big_five = predictor.predict_big_five(infp_text)
    traits = ["Extraversion", "Neuroticism", "Agreeableness", "Conscientiousness", "Openness"]
    print("Big Five Traits:")
    for i, trait in enumerate(traits):
        value = "High" if big_five[i] == 1 else "Low"
        print(f"  {trait}: {value}")

if __name__ == "__main__":
    test_mbti_descriptions()
