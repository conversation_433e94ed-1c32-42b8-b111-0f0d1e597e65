#!/usr/bin/env python3
"""
Test script for ESTP detection in the personality analysis system.
"""

from simple_personality_predictor import SimplePersonalityPredictor

def test_estp_detection():
    """Test the personality predictor with ESTP text."""
    
    # Initialize the predictor
    predictor = SimplePersonalityPredictor()
    
    # ESTP text from the user
    estp_text = """I love jumping into action and figuring things out on the spot.
I'm bold, energetic, and not afraid to take risks.
Rules don't bother me much—I prefer real-world results.
Life is more fun when I'm moving, solving, and winning"""
    
    # Analyze the text
    print("=== Analyzing ESTP Text ===\n")
    
    # Predict Big Five traits
    big_five = predictor.predict_big_five(estp_text)
    print("Big Five Traits:")
    traits = ["Extraversion", "Neuroticism", "Agreeableness", "Conscientiousness", "Openness"]
    for i, trait in enumerate(traits):
        value = "High" if big_five[i] == 1 else "Low"
        print(f"  {trait}: {value}")
    
    # Predict MBTI type
    mbti = predictor.predict_mbti(estp_text)
    print(f"\nMBTI Type: {mbti}")
    
    # Check if prediction is correct
    is_correct = mbti == "ESTP"
    print(f"Correct ESTP prediction: {'✓' if is_correct else '✗'}")
    
    # Get career suggestions
    careers = predictor.suggest_careers(big_five, mbti)
    print("\nCareer Suggestions:")
    for career in careers:
        print(f"  - {career}")

if __name__ == "__main__":
    test_estp_detection()
