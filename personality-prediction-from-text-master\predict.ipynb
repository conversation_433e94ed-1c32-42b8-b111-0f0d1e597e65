{"cells": [{"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["import pickle\n", "from sklearn.feature_extraction.text import CountVectorizer\n", "import plotly.express as px\n", "import pandas as pd\n", "import re\n"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"ename": "ValueError", "evalue": "node array from the pickle has an incompatible dtype:\n- expected: {'names': ['left_child', 'right_child', 'feature', 'threshold', 'impurity', 'n_node_samples', 'weighted_n_node_samples', 'missing_go_to_left'], 'formats': ['<i8', '<i8', '<i8', '<f8', '<f8', '<i8', '<f8', 'u1'], 'offsets': [0, 8, 16, 24, 32, 40, 48, 56], 'itemsize': 64}\n- got     : [('left_child', '<i8'), ('right_child', '<i8'), ('feature', '<i8'), ('threshold', '<f8'), ('impurity', '<f8'), ('n_node_samples', '<i8'), ('weighted_n_node_samples', '<f8')]", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31m<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\u001b[39m                                <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[9]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m cEXT = pickle.load( \u001b[38;5;28mopen\u001b[39m( \u001b[33m\"\u001b[39m\u001b[33mdata/models/cEXT.p\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mrb\u001b[39m\u001b[33m\"\u001b[39m))\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m cNEU = \u001b[43mpickle\u001b[49m\u001b[43m.\u001b[49m\u001b[43mload\u001b[49m\u001b[43m(\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mopen\u001b[39;49m\u001b[43m(\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mdata/models/cNEU.p\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mrb\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m      3\u001b[39m cAGR = pickle.load( \u001b[38;5;28mopen\u001b[39m( \u001b[33m\"\u001b[39m\u001b[33mdata/models/cAGR.p\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mrb\u001b[39m\u001b[33m\"\u001b[39m))\n\u001b[32m      4\u001b[39m cCON = pickle.load( \u001b[38;5;28mopen\u001b[39m( \u001b[33m\"\u001b[39m\u001b[33mdata/models/cCON.p\u001b[39m\u001b[33m\"\u001b[39m, \u001b[33m\"\u001b[39m\u001b[33mrb\u001b[39m\u001b[33m\"\u001b[39m))\n", "\u001b[36mFile \u001b[39m\u001b[32m_tree.pyx:848\u001b[39m, in \u001b[36msklearn.tree._tree.Tree.__setstate__\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[36mFile \u001b[39m\u001b[32m_tree.pyx:1554\u001b[39m, in \u001b[36msklearn.tree._tree._check_node_n<PERSON>ray\u001b[39m\u001b[34m()\u001b[39m\n", "\u001b[31mValueError\u001b[39m: node array from the pickle has an incompatible dtype:\n- expected: {'names': ['left_child', 'right_child', 'feature', 'threshold', 'impurity', 'n_node_samples', 'weighted_n_node_samples', 'missing_go_to_left'], 'formats': ['<i8', '<i8', '<i8', '<f8', '<f8', '<i8', '<f8', 'u1'], 'offsets': [0, 8, 16, 24, 32, 40, 48, 56], 'itemsize': 64}\n- got     : [('left_child', '<i8'), ('right_child', '<i8'), ('feature', '<i8'), ('threshold', '<f8'), ('impurity', '<f8'), ('n_node_samples', '<i8'), ('weighted_n_node_samples', '<f8')]"]}], "source": ["cEXT = pickle.load( open( \"data/models/cEXT.p\", \"rb\"))\n", "cNEU = pickle.load( open( \"data/models/cNEU.p\", \"rb\"))\n", "cAGR = pickle.load( open( \"data/models/cAGR.p\", \"rb\"))\n", "cCON = pickle.load( open( \"data/models/cCON.p\", \"rb\"))\n", "cOPN = pickle.load( open( \"data/models/cOPN.p\", \"rb\"))\n", "vectorizer_31 = pickle.load( open( \"data/models/vectorizer_31.p\", \"rb\"))\n", "vectorizer_30 = pickle.load( open( \"data/models/vectorizer_30.p\", \"rb\"))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def predict_personality(text):\n", "    scentences = re.split(\"(?<=[.!?]) +\", text)\n", "    text_vector_31 = vectorizer_31.transform(scentences)\n", "    text_vector_30 = vectorizer_30.transform(scentences)\n", "    EXT = cEXT.predict(text_vector_31)\n", "    NEU = cNEU.predict(text_vector_30)\n", "    AGR = cAGR.predict(text_vector_31)\n", "    CON = cCON.predict(text_vector_31)\n", "    OPN = cOPN.predict(text_vector_31)\n", "    return [EXT[0], NEU[0], AGR[0], CON[0], OPN[0]]\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["text = 'It is important to note that each of the five personality factors represents a range between two extremes. For example, extraversion represents a continuum between extreme extraversion and extreme introversion. In the real world, most people lie somewhere in between the two polar ends of each dimension. These five categories are usually described as follows.'"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["predictions = predict_personality(text)\n", "print(\"predicted personality:\", predictions)\n", "df = pd.DataFrame(dict(r=predictions, theta=['EXT','NEU','AGR', 'CON', 'OPN']))\n", "fig = px.line_polar(df, r='r', theta='theta', line_close=True)\n", "fig.show()\n", "\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}