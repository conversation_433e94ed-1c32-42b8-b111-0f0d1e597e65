#!/usr/bin/env python3
"""
Test script for ENFJ detection in the personality analysis system.
"""

from simple_personality_predictor import SimplePersonalityPredictor

def test_enfj_detection():
    """Test the personality predictor with ENFJ text."""
    
    # Initialize the predictor
    predictor = SimplePersonalityPredictor()
    
    # ENFJ text from the user
    enfj_text = """I lead with vision and lift with love.
You shine brighter when someone believes in you—
I do.
Together, we become more than we imagined."""
    
    # Analyze the text
    print("=== Analyzing ENFJ Text ===\n")
    
    # Predict Big Five traits
    big_five = predictor.predict_big_five(enfj_text)
    print("Big Five Traits:")
    traits = ["Extraversion", "Neuroticism", "Agreeableness", "Conscientiousness", "Openness"]
    for i, trait in enumerate(traits):
        value = "High" if big_five[i] == 1 else "Low"
        print(f"  {trait}: {value}")
    
    # Predict MBTI type
    mbti = predictor.predict_mbti(enfj_text)
    print(f"\nMBTI Type: {mbti}")
    
    # Check if prediction is correct
    is_correct = mbti == "ENFJ"
    print(f"Correct ENFJ prediction: {'✓' if is_correct else '✗'}")
    
    # Get career suggestions
    careers = predictor.suggest_careers(big_five, mbti)
    print("\nCareer Suggestions:")
    for career in careers:
        print(f"  - {career}")

if __name__ == "__main__":
    test_enfj_detection()
