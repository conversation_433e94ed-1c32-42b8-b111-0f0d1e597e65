{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Improved Personality Analysis\n", "\n", "This notebook demonstrates the improved personality analysis system that provides more accurate MBTI type predictions and Big Five trait assessments."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Models loaded successfully!\n"]}], "source": ["# Import the improved personality predictor\n", "from simple_personality_predictor import SimplePersonalityPredictor\n", "\n", "# Initialize the predictor\n", "predictor = SimplePersonalityPredictor()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Introduction to Personality Types\n", "\n", "This system analyzes text to predict:\n", "\n", "1. **Big Five Personality Traits**: Extraversion, Neuroticism, Agreeableness, Conscientiousness, and Openness\n", "2. **MBTI Personality Type**: One of the 16 Myers-Briggs Type Indicator personality types\n", "\n", "The system has been improved to provide more accurate predictions, especially for the MBTI types."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. The Big Five Personality Traits\n", "\n", "The Big Five personality traits are the most widely accepted model of personality in psychology:\n", "\n", "- **Extraversion**: Sociability, assertiveness, and emotional expressiveness\n", "- **Neuroticism**: Emotional instability, anxiety, and negative emotions\n", "- **Agreeableness**: Trust, altruism, kindness, and affection\n", "- **Conscientiousness**: Thoughtfulness, impulse control, and goal-directed behaviors\n", "- **Openness**: Imagination, creativity, and curiosity"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. The 16 MBTI Personality Types\n", "\n", "The Myers-Briggs Type Indicator (MBTI) categorizes personalities into 16 types based on four dimensions:\n", "\n", "- **Extraversion (E) vs. Introversion (I)**: Where you focus your attention and get your energy\n", "- **Sensing (S) vs. Intuition (N)**: How you take in information\n", "- **Thinking (T) vs. Feeling (F)**: How you make decisions\n", "- **Judging (J) vs. Perceiving (P)**: How you deal with the outer world\n", "\n", "The 16 types are organized into four groups:\n", "\n", "### 🧠 Analysts\n", "- **INTJ – The Architect**\n", "- **INTP – The Logician**\n", "- **<PERSON><PERSON><PERSON> – The Commander**\n", "- **ENTP – The Debater**\n", "\n", "### 💓 Diplomats\n", "- **INFJ – The Advocate**\n", "- **INFP – The Mediator**\n", "- **ENFJ – The Protagonist**\n", "- **ENFP – The Campaigner**\n", "\n", "### 🛠️ Sentinels\n", "- **ISTJ – The Inspector**\n", "- **ISFJ – The Defender**\n", "- **<PERSON><PERSON><PERSON> – The Executive**\n", "- **<PERSON><PERSON><PERSON> – The Consul**\n", "\n", "### 🌊 Explorers\n", "- **ISTP – The Virtuoso**\n", "- **ISFP – The Adventurer**\n", "- **ESTP – The Entrepreneur**\n", "- **ESFP – The Entertainer**"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Example Analysis\n", "\n", "Let's analyze some example texts to see how the system works:"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== Analyzing INFP Description ===\n", "\n", "Big Five Traits:\n", "  Extraversion: Low\n", "  Neuroticism: Low\n", "  Agreeableness: High\n", "  Conscientiousness: High\n", "  Openness: High\n", "\n", "MBTI Type: INFP\n", "\n", "Career Suggestions:\n", "  - Writer\n", "  - Psychologist\n", "  - Innovation Consultant\n", "  - Teacher\n", "  - Graphic Designer\n", "\n", "Communication Style: Thoughtful and Supportive\n", "Strengths:\n", "  - Listening attentively\n", "  - Creating harmony in discussions\n", "  - Communicating in an organized, structured way\n", "  - Bringing creative ideas to discussions\n", "  - Maintaining composure during difficult conversations\n", "Tips:\n", "  - Prepare talking points in advance for meetings\n", "\n", "Leadership Style: Structured and Supportive Leader\n", "Strengths:\n", "  - Creating clear processes and expectations\n", "  - Encouraging innovation and creative solutions\n", "  - Building team cohesion and morale\n", "  - Staying calm under pressure and managing stress effectively\n"]}], "source": ["# Example 1: INFP description\n", "infp_text = \"\"\"I live through meaning, not just moments.\n", "I feel everything deeply and value inner truth.\n", "I may be quiet, but my imagination is loud.\n", "Kindness and authenticity are my compass.\"\"\"\n", "\n", "# Analyze the text\n", "print(\"=== Analyzing INFP Description ===\\n\")\n", "\n", "# Predict Big Five traits\n", "big_five = predictor.predict_big_five(infp_text)\n", "print(\"Big Five Traits:\")\n", "traits = [\"Extraversion\", \"Neuroticism\", \"Agreeableness\", \"Conscientiousness\", \"Openness\"]\n", "for i, trait in enumerate(traits):\n", "    value = \"High\" if big_five[i] == 1 else \"Low\"\n", "    print(f\"  {trait}: {value}\")\n", "\n", "# Predict MBTI type\n", "mbti = predictor.predict_mbti(infp_text)\n", "print(f\"\\nMBTI Type: {mbti}\")\n", "\n", "# Get career suggestions\n", "careers = predictor.suggest_careers(big_five, mbti)\n", "print(\"\\nCareer Suggestions:\")\n", "for career in careers:\n", "    print(f\"  - {career}\")\n", "\n", "# Analyze communication style\n", "comm_style = predictor.analyze_communication_style(big_five, mbti)\n", "print(f\"\\nCommunication Style: {comm_style['primary_style']}\")\n", "print(\"Strengths:\")\n", "for strength in comm_style['strengths']:\n", "    print(f\"  - {strength}\")\n", "print(\"Tips:\")\n", "for tip in comm_style['tips'][:2]:\n", "    print(f\"  - {tip}\")\n", "\n", "# Analyze leadership style\n", "lead_style = predictor.analyze_leadership_style(big_five, mbti)\n", "print(f\"\\nLeadership Style: {lead_style['primary_style']}\")\n", "print(\"Strengths:\")\n", "for strength in lead_style['strengths']:\n", "    print(f\"  - {strength}\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== Analyzing INTJ Description ===\n", "\n", "Big Five Traits:\n", "  Extraversion: Low\n", "  Neuroticism: Low\n", "  Agreeableness: Low\n", "  Conscientiousness: High\n", "  Openness: High\n", "\n", "MBTI Type: INTJ\n", "\n", "Career Suggestions:\n", "  - Systems Analyst\n", "  - UX Designer\n", "  - Innovation Consultant\n", "  - Research Scientist\n", "  - Software Developer\n"]}], "source": ["# Example 2: INTJ description\n", "intj_text = \"\"\"I see the big picture and plan steps ahead.\n", "I value logic and independence deeply.\n", "Emotions don't cloud my judgment, but I respect them.\n", "I quietly strive to improve the world through strategy.\"\"\"\n", "\n", "# Analyze the text\n", "print(\"=== Analyzing INTJ Description ===\\n\")\n", "\n", "# Predict Big Five traits\n", "big_five = predictor.predict_big_five(intj_text)\n", "print(\"Big Five Traits:\")\n", "traits = [\"Extraversion\", \"Neuroticism\", \"Agreeableness\", \"Conscientiousness\", \"Openness\"]\n", "for i, trait in enumerate(traits):\n", "    value = \"High\" if big_five[i] == 1 else \"Low\"\n", "    print(f\"  {trait}: {value}\")\n", "\n", "# Predict MBTI type\n", "mbti = predictor.predict_mbti(intj_text)\n", "print(f\"\\nMBTI Type: {mbti}\")\n", "\n", "# Get career suggestions\n", "careers = predictor.suggest_careers(big_five, mbti)\n", "print(\"\\nCareer Suggestions:\")\n", "for career in careers:\n", "    print(f\"  - {career}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Analyze Your Own Text\n", "\n", "Now it's your turn! Enter your own text below to get a comprehensive personality analysis."]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Big Five Traits:\n", "  Extraversion: High\n", "  Neuroticism: Low\n", "  Agreeableness: High\n", "  Conscientiousness: Low\n", "  Openness: High\n", "\n", "MBTI Type: ENFJ\n", "\n", "Career Suggestions:\n", "  - HR Manager\n", "  - Training Manager\n", "  - Event Coordinator\n", "  - Innovation Consultant\n", "  - Teacher\n", "\n", "Communication Style: Confident and Collaborative\n", "Strengths:\n", "  - Engaging others in conversation\n", "  - Creating harmony in discussions\n", "  - Bringing creative ideas to discussions\n", "  - Maintaining composure during difficult conversations\n", "\n", "Leadership Style: Visionary and Innovative Leader\n", "Strengths:\n", "  - Encouraging innovation and creative solutions\n", "  - Building team cohesion and morale\n", "  - Energizing and motivating team members\n", "  - Staying calm under pressure and managing stress effectively\n"]}], "source": ["# Enter your text here\n", "your_text = \"\"\"I enjoy spending time with friends and meeting new people. \n", "I'm always looking for new experiences and adventures. \n", "I like to plan ahead and stay organized. \n", "I try to be considerate of others' feelings and help when I can.\"\"\"\n", "\n", "# Analyze your text\n", "your_results = predictor.predict_big_five(your_text)\n", "print(\"Big Five Traits:\")\n", "traits = [\"Extraversion\", \"Neuroticism\", \"Agreeableness\", \"Conscientiousness\", \"Openness\"]\n", "for i, trait in enumerate(traits):\n", "    value = \"High\" if your_results[i] == 1 else \"Low\"\n", "    print(f\"  {trait}: {value}\")\n", "\n", "# Predict MBTI type\n", "mbti = predictor.predict_mbti(your_text)\n", "print(f\"\\nMBTI Type: {mbti}\")\n", "\n", "# Get career suggestions\n", "careers = predictor.suggest_careers(your_results, mbti)\n", "print(\"\\nCareer Suggestions:\")\n", "for career in careers:\n", "    print(f\"  - {career}\")\n", "\n", "# Analyze communication style\n", "comm_style = predictor.analyze_communication_style(your_results, mbti)\n", "print(f\"\\nCommunication Style: {comm_style['primary_style']}\")\n", "print(\"Strengths:\")\n", "for strength in comm_style['strengths']:\n", "    print(f\"  - {strength}\")\n", "\n", "# Analyze leadership style\n", "lead_style = predictor.analyze_leadership_style(your_results, mbti)\n", "print(f\"\\nLeadership Style: {lead_style['primary_style']}\")\n", "print(\"Strengths:\")\n", "for strength in lead_style['strengths']:\n", "    print(f\"  - {strength}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Your Personality Analysis\n", "\n", "Now it's your turn! Enter your own text below to get a comprehensive personality analysis."]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Big Five Traits:\n", "  Extraversion: High\n", "  Neuroticism: Low\n", "  Agreeableness: Low\n", "  Conscientiousness: High\n", "  Openness: High\n", "\n", "MBTI Type: ENFJ\n", "\n", "Career Suggestions:\n", "  - HR Manager\n", "  - Training Manager\n", "  - Event Coordinator\n", "  - Innovation Consultant\n", "  - Teacher\n"]}], "source": ["# Enter your text here\n", "your_text = \"\"\"I lead with vision and lift with love.\n", "You shine brighter when someone believes in you—\n", "I do.\n", "Together, we become more than we imagined.\"\"\"\n", "\n", "# Analyze your text\n", "your_results = predictor.predict_big_five(your_text)\n", "print(\"Big Five Traits:\")\n", "traits = [\"Extraversion\", \"Neuroticism\", \"Agreeableness\", \"Conscientiousness\", \"Openness\"]\n", "for i, trait in enumerate(traits):\n", "    value = \"High\" if your_results[i] == 1 else \"Low\"\n", "    print(f\"  {trait}: {value}\")\n", "\n", "# Predict MBTI type\n", "mbti = predictor.predict_mbti(your_text)\n", "print(f\"\\nMBTI Type: {mbti}\")\n", "\n", "# Get career suggestions\n", "careers = predictor.suggest_careers(your_results, mbti)\n", "print(\"\\nCareer Suggestions:\")\n", "for career in careers:\n", "    print(f\"  - {career}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}