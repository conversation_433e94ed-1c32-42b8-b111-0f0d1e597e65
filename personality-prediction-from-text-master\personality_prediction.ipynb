{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Personality Prediction from Text\n", "\n", "This notebook demonstrates how to predict personality traits from text using a workaround for the cNEU model compatibility issue."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Direct approach with workaround\n", "\n", "First, let's implement a direct workaround for the cNEU model issue."]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pickle\n", "import re\n", "import warnings\n", "import numpy as np\n", "import pandas as pd\n", "import plotly.express as px\n", "\n", "# Suppress scikit-learn warnings about unpickling from older versions\n", "warnings.filterwarnings(\"ignore\", category=UserWarning)"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Loading models...\n", "Models loaded successfully!\n"]}], "source": ["# Load models (skipping cNEU which has compatibility issues)\n", "print(\"Loading models...\")\n", "cEXT = pickle.load(open(\"data/models/cEXT.p\", \"rb\"))\n", "# Skip cNEU due to compatibility issues\n", "cAGR = pickle.load(open(\"data/models/cAGR.p\", \"rb\"))\n", "cCON = pickle.load(open(\"data/models/cCON.p\", \"rb\"))\n", "cOPN = pickle.load(open(\"data/models/cOPN.p\", \"rb\"))\n", "vectorizer_31 = pickle.load(open(\"data/models/vectorizer_31.p\", \"rb\"))\n", "vectorizer_30 = pickle.load(open(\"data/models/vectorizer_30.p\", \"rb\"))\n", "print(\"Models loaded successfully!\")"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["def predict_personality(text):\n", "    \"\"\"Predict personality traits from text.\"\"\"\n", "    # Split text into sentences\n", "    sentences = re.split(\"(?<=[.!?]) +\", text)\n", "    \n", "    # Transform text using vectorizers\n", "    text_vector_31 = vectorizer_31.transform(sentences)\n", "    \n", "    # Predict personality traits\n", "    EXT = cEXT.predict(text_vector_31)\n", "    NEU = np.array([0])  # Default value due to model compatibility issues\n", "    AGR = cAGR.predict(text_vector_31)\n", "    CON = cCON.predict(text_vector_31)\n", "    OPN = cOPN.predict(text_vector_31)\n", "    \n", "    return [int(EXT[0]), int(NEU[0]), int(AGR[0]), int(CON[0]), int(OPN[0])]"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [], "source": ["def interpret_results(result):\n", "    \"\"\"Interpret the prediction results.\"\"\"\n", "    traits = [\"Extraversion\", \"Neuroticism\", \"Agreeableness\", \"Conscientiousness\", \"Openness\"]\n", "    interpretation = {}\n", "    \n", "    for i, trait in enumerate(traits):\n", "        value = \"High\" if result[i] == 1 else \"Low\"\n", "        interpretation[trait] = value\n", "    \n", "    # Add a note about Neuroticism\n", "    interpretation[\"Neuroticism\"] += \" (default value due to model compatibility issues)\"\n", "    \n", "    return interpretation"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["def visualize_personality(result):\n", "    \"\"\"Create a radar chart to visualize personality traits.\"\"\"\n", "    # Add the first trait again at the end to close the polygon\n", "    r = result + [result[0]]\n", "    \n", "    # Create labels\n", "    theta = [\"EXT\", \"NEU\", \"AGR\", \"CON\", \"OPN\", \"EXT\"]\n", "    \n", "    # Create figure\n", "    fig = px.line_polar(\n", "        r=r, \n", "        theta=theta, \n", "        line_close=True,\n", "        range_r=[0, 1],\n", "        title=\"Personality Traits\"\n", "    )\n", "    \n", "    # Update layout\n", "    fig.update_layout(\n", "        polar=dict(\n", "            radialaxis=dict(visible=True, range=[0, 1]),\n", "            angularaxis=dict(direction=\"clockwise\", rotation=90)\n", "        )\n", "    )\n", "    \n", "    # Fill the area\n", "    fig.update_traces(fill='toself')\n", "    \n", "    return fig"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Test with example text"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Prediction result: [1, 0, 0, 0, 1]\n", "\n", "Personality Prediction Results:\n", "  Extraversion: High\n", "  Neuroticism: Low (default value due to model compatibility issues)\n", "  Agreeableness: Low\n", "  Conscientiousness: Low\n", "  Openness: High\n"]}], "source": ["# Example text\n", "text = 'It is important to note that each of the five personality factors represents a range between two extremes. For example, extraversion represents a continuum between extreme extraversion and extreme introversion. In the real world, most people lie somewhere in between the two polar ends of each dimension. These five categories are usually described as follows.'\n", "\n", "# Predict personality\n", "result = predict_personality(text)\n", "print(\"Prediction result:\", result)\n", "\n", "# Interpret results\n", "interpretation = interpret_results(result)\n", "print(\"\\nPersonality Prediction Results:\")\n", "for trait, value in interpretation.items():\n", "    print(f\"  {trait}: {value}\")"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"fill": "toself", "hovertemplate": "r=%{r}<br>theta=%{theta}<extra></extra>", "legendgroup": "", "line": {"color": "#636efa", "dash": "solid"}, "marker": {"symbol": "circle"}, "mode": "lines", "name": "", "r": {"bdata": "AQAAAAEBAQ==", "dtype": "i1"}, "showlegend": false, "subplot": "polar", "theta": ["EXT", "NEU", "AGR", "CON", "OPN", "EXT", "EXT"], "type": "scatterpolar"}], "layout": {"legend": {"tracegroupgap": 0}, "polar": {"angularaxis": {"direction": "clockwise", "rotation": 90}, "domain": {"x": [0, 1], "y": [0, 1]}, "radialaxis": {"range": [0, 1], "visible": true}}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermap": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermap"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "Personality Traits"}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualize the results\n", "fig = visualize_personality(result)\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Try with your own text\n", "\n", "Enter your text in the cell below and run the prediction."]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Prediction result: [1, 0, 1, 0, 1]\n", "\n", "Personality Prediction Results:\n", "  Extraversion: High\n", "  Neuroticism: Low (default value due to model compatibility issues)\n", "  Agreeableness: High\n", "  Conscientiousness: Low\n", "  Openness: High\n"]}], "source": ["# Enter your own text here\n", "your_text = \"I enjoy spending time with friends and meeting new people. I'm always looking for new experiences and adventures. I like to plan ahead and stay organized. I try to be considerate of others' feelings and help when I can.\"\n", "\n", "# Predict personality\n", "your_result = predict_personality(your_text)\n", "print(\"Prediction result:\", your_result)\n", "\n", "# Interpret results\n", "your_interpretation = interpret_results(your_result)\n", "print(\"\\nPersonality Prediction Results:\")\n", "for trait, value in your_interpretation.items():\n", "    print(f\"  {trait}: {value}\")"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"fill": "toself", "hovertemplate": "r=%{r}<br>theta=%{theta}<extra></extra>", "legendgroup": "", "line": {"color": "#636efa", "dash": "solid"}, "marker": {"symbol": "circle"}, "mode": "lines", "name": "", "r": {"bdata": "AQABAAEBAQ==", "dtype": "i1"}, "showlegend": false, "subplot": "polar", "theta": ["EXT", "NEU", "AGR", "CON", "OPN", "EXT", "EXT"], "type": "scatterpolar"}], "layout": {"legend": {"tracegroupgap": 0}, "polar": {"angularaxis": {"direction": "clockwise", "rotation": 90}, "domain": {"x": [0, 1], "y": [0, 1]}, "radialaxis": {"range": [0, 1], "visible": true}}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermap": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermap"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "Personality Traits"}}}}, "metadata": {}, "output_type": "display_data"}], "source": ["# Visualize the results\n", "your_fig = visualize_personality(your_result)\n", "your_fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Interactive text input\n", "\n", "You can use the cell below to analyze any text you want."]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [], "source": ["def analyze_text(text):\n", "    \"\"\"Analyze text and display personality prediction results.\"\"\"\n", "    # Predict personality\n", "    result = predict_personality(text)\n", "    print(\"Prediction result:\", result)\n", "    \n", "    # Interpret results\n", "    interpretation = interpret_results(result)\n", "    print(\"\\nPersonality Prediction Results:\")\n", "    for trait, value in interpretation.items():\n", "        print(f\"  {trait}: {value}\")\n", "    \n", "    # Visualize the results\n", "    fig = visualize_personality(result)\n", "    fig.show()\n", "    \n", "    return result"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Prediction result: [1, 0, 0, 1, 1]\n", "\n", "Personality Prediction Results:\n", "  Extraversion: High\n", "  Neuroticism: Low (default value due to model compatibility issues)\n", "  Agreeableness: Low\n", "  Conscientiousness: High\n", "  Openness: High\n"]}, {"data": {"application/vnd.plotly.v1+json": {"config": {"plotlyServerURL": "https://plot.ly"}, "data": [{"fill": "toself", "hovertemplate": "r=%{r}<br>theta=%{theta}<extra></extra>", "legendgroup": "", "line": {"color": "#636efa", "dash": "solid"}, "marker": {"symbol": "circle"}, "mode": "lines", "name": "", "r": {"bdata": "AQAAAQEBAQ==", "dtype": "i1"}, "showlegend": false, "subplot": "polar", "theta": ["EXT", "NEU", "AGR", "CON", "OPN", "EXT", "EXT"], "type": "scatterpolar"}], "layout": {"legend": {"tracegroupgap": 0}, "polar": {"angularaxis": {"direction": "clockwise", "rotation": 90}, "domain": {"x": [0, 1], "y": [0, 1]}, "radialaxis": {"range": [0, 1], "visible": true}}, "template": {"data": {"bar": [{"error_x": {"color": "#2a3f5f"}, "error_y": {"color": "#2a3f5f"}, "marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "bar"}], "barpolar": [{"marker": {"line": {"color": "#E5ECF6", "width": 0.5}, "pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "barpolar"}], "carpet": [{"aaxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "baxis": {"endlinecolor": "#2a3f5f", "gridcolor": "white", "linecolor": "white", "minorgridcolor": "white", "startlinecolor": "#2a3f5f"}, "type": "carpet"}], "choropleth": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "choropleth"}], "contour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "contour"}], "contourcarpet": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "contourcarpet"}], "heatmap": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "heatmap"}], "histogram": [{"marker": {"pattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}}, "type": "histogram"}], "histogram2d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2d"}], "histogram2dcontour": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "histogram2dcontour"}], "mesh3d": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "type": "mesh3d"}], "parcoords": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "parcoords"}], "pie": [{"automargin": true, "type": "pie"}], "scatter": [{"fillpattern": {"fillmode": "overlay", "size": 10, "solidity": 0.2}, "type": "scatter"}], "scatter3d": [{"line": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatter3d"}], "scattercarpet": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattercarpet"}], "scattergeo": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergeo"}], "scattergl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattergl"}], "scattermap": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermap"}], "scattermapbox": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scattermapbox"}], "scatterpolar": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolar"}], "scatterpolargl": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterpolargl"}], "scatterternary": [{"marker": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "type": "scatterternary"}], "surface": [{"colorbar": {"outlinewidth": 0, "ticks": ""}, "colorscale": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "type": "surface"}], "table": [{"cells": {"fill": {"color": "#EBF0F8"}, "line": {"color": "white"}}, "header": {"fill": {"color": "#C8D4E3"}, "line": {"color": "white"}}, "type": "table"}]}, "layout": {"annotationdefaults": {"arrowcolor": "#2a3f5f", "arrowhead": 0, "arrowwidth": 1}, "autotypenumbers": "strict", "coloraxis": {"colorbar": {"outlinewidth": 0, "ticks": ""}}, "colorscale": {"diverging": [[0, "#8e0152"], [0.1, "#c51b7d"], [0.2, "#de77ae"], [0.3, "#f1b6da"], [0.4, "#fde0ef"], [0.5, "#f7f7f7"], [0.6, "#e6f5d0"], [0.7, "#b8e186"], [0.8, "#7fbc41"], [0.9, "#4d9221"], [1, "#276419"]], "sequential": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]], "sequentialminus": [[0, "#0d0887"], [0.1111111111111111, "#46039f"], [0.2222222222222222, "#7201a8"], [0.3333333333333333, "#9c179e"], [0.4444444444444444, "#bd3786"], [0.5555555555555556, "#d8576b"], [0.6666666666666666, "#ed7953"], [0.7777777777777778, "#fb9f3a"], [0.8888888888888888, "#fdca26"], [1, "#f0f921"]]}, "colorway": ["#636efa", "#EF553B", "#00cc96", "#ab63fa", "#FFA15A", "#19d3f3", "#FF6692", "#B6E880", "#FF97FF", "#FECB52"], "font": {"color": "#2a3f5f"}, "geo": {"bgcolor": "white", "lakecolor": "white", "landcolor": "#E5ECF6", "showlakes": true, "showland": true, "subunitcolor": "white"}, "hoverlabel": {"align": "left"}, "hovermode": "closest", "mapbox": {"style": "light"}, "paper_bgcolor": "white", "plot_bgcolor": "#E5ECF6", "polar": {"angularaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "radialaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "scene": {"xaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "yaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}, "zaxis": {"backgroundcolor": "#E5ECF6", "gridcolor": "white", "gridwidth": 2, "linecolor": "white", "showbackground": true, "ticks": "", "zerolinecolor": "white"}}, "shapedefaults": {"line": {"color": "#2a3f5f"}}, "ternary": {"aaxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "baxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}, "bgcolor": "#E5ECF6", "caxis": {"gridcolor": "white", "linecolor": "white", "ticks": ""}}, "title": {"x": 0.05}, "xaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}, "yaxis": {"automargin": true, "gridcolor": "white", "linecolor": "white", "ticks": "", "title": {"standoff": 15}, "zerolinecolor": "white", "zerolinewidth": 2}}}, "title": {"text": "Personality Traits"}}}, "image/png": "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"}, "metadata": {}, "output_type": "display_data"}, {"data": {"text/plain": ["[1, 0, 0, 1, 1]"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# Enter your text here\n", "new_text = \"\"\"Enter your text here. The more text you provide, the more accurate the prediction will be.\n", "Try to write at least a few sentences about your thoughts, feelings, or experiences.\"\"\"\n", "\n", "# Analyze the text\n", "analyze_text(new_text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## Note about Neuroticism prediction\n", "\n", "The Neuroticism (NEU) prediction is using a default value of \"Low\" (0) due to compatibility issues with the cNEU model. This is because the model was created with an older version of scikit-learn (0.22.1) that has a different internal structure for decision trees than the current version.\n", "\n", "If you need accurate Neuroticism predictions, you would need to:\n", "1. Install scikit-learn version 0.22.1 in a separate environment, or\n", "2. Retrain the model with your current scikit-learn version"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.12.3"}}, "nbformat": 4, "nbformat_minor": 4}