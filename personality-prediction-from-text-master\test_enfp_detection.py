#!/usr/bin/env python3
"""
Test script for ENFP detection in the personality analysis system.
"""

from simple_personality_predictor import SimplePersonalityPredictor

def test_enfp_detection():
    """Test the personality predictor with ENFP text."""
    
    # Initialize the predictor
    predictor = SimplePersonalityPredictor()
    
    # ENFP text from the user
    enfp_text = """I see potential everywhere and love exploring new ideas.
I follow my heart, even if the path is messy.
People energize me, and I thrive on deep, authentic connections.
I believe life is meant to be felt, not just figured out."""
    
    # Analyze the text
    print("=== Analyzing ENFP Text ===\n")
    
    # Predict Big Five traits
    big_five = predictor.predict_big_five(enfp_text)
    print("Big Five Traits:")
    traits = ["Extraversion", "Neuroticism", "Agreeableness", "Conscientiousness", "Openness"]
    for i, trait in enumerate(traits):
        value = "High" if big_five[i] == 1 else "Low"
        print(f"  {trait}: {value}")
    
    # Predict MBTI type
    mbti = predictor.predict_mbti(enfp_text)
    print(f"\nMBTI Type: {mbti}")
    
    # Check if prediction is correct
    is_correct = mbti == "ENFP"
    print(f"Correct ENFP prediction: {'✓' if is_correct else '✗'}")
    
    # Get career suggestions
    careers = predictor.suggest_careers(big_five, mbti)
    print("\nCareer Suggestions:")
    for career in careers:
        print(f"  - {career}")

if __name__ == "__main__":
    test_enfp_detection()
