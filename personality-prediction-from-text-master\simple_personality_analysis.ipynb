{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Simple Personality Analysis\n", "\n", "This notebook provides an enhanced personality analysis with:\n", "1. **Balanced Extraversion prediction** - Corrects the bias in the original model\n", "2. **Text-based Neuroticism estimation** - Uses lexical analysis instead of a default value\n", "3. **Additional personality insights** - MBTI type, career suggestions, communication style, and leadership style\n", "\n", "These improvements make the analysis more accurate and comprehensive, providing insights that will catch an interviewer's attention."]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 1. Setup and Imports"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"ename": "ModuleNotFoundError", "evalue": "No module named 'numpy'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mModuleNotFoundError\u001b[39m                       <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[2]\u001b[39m\u001b[32m, line 2\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mwarnings\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m2\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnumpy\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mnp\u001b[39;00m\n\u001b[32m      3\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpandas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpd\u001b[39;00m\n\u001b[32m      4\u001b[39m \u001b[38;5;28;01mimport\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mplotly\u001b[39;00m\u001b[34;01m.\u001b[39;00m\u001b[34;01mexpress\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[38;5;28;01mas\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34;01mpx\u001b[39;00m\n", "\u001b[31mModuleNotFoundError\u001b[39m: No module named 'numpy'"]}], "source": ["import warnings\n", "import numpy as np\n", "import pandas as pd\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "from plotly.subplots import make_subplots\n", "\n", "# Import our simple personality predictor\n", "from simple_personality_predictor import SimplePersonalityPredictor\n", "\n", "# Suppress warnings\n", "warnings.filterwarnings(\"ignore\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Initialize the predictor\n", "predictor = SimplePersonalityPredictor()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 2. Visualization Functions"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def visualize_big_five(result, title=\"Big Five Personality Traits\"):\n", "    \"\"\"Create a radar chart to visualize Big Five personality traits.\"\"\"\n", "    # Add the first trait again at the end to close the polygon\n", "    r = result + [result[0]]\n", "    \n", "    # Create labels\n", "    theta = [\"Extraversion\", \"Neuroticism\", \"Agreeableness\", \"Conscientiousness\", \"Openness\", \"Extraversion\"]\n", "    \n", "    # Create figure\n", "    fig = px.line_polar(\n", "        r=r, \n", "        theta=theta, \n", "        line_close=True,\n", "        range_r=[0, 1],\n", "        title=title\n", "    )\n", "    \n", "    # Update layout\n", "    fig.update_layout(\n", "        polar=dict(\n", "            radialaxis=dict(visible=True, range=[0, 1]),\n", "            angularaxis=dict(direction=\"clockwise\", rotation=90)\n", "        )\n", "    )\n", "    \n", "    # Fill the area\n", "    fig.update_traces(fill='toself')\n", "    \n", "    return fig"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def visualize_mbti(mbti_type):\n", "    \"\"\"Create a visualization for MBTI type.\"\"\"\n", "    # Extract the individual preferences\n", "    e_i = 1 if mbti_type[0] == 'E' else 0\n", "    s_n = 1 if mbti_type[1] == 'N' else 0\n", "    t_f = 1 if mbti_type[2] == 'F' else 0\n", "    j_p = 1 if mbti_type[3] == 'P' else 0\n", "    \n", "    # Create a figure with subplots\n", "    fig = make_subplots(rows=2, cols=2, specs=[[{\"type\": \"domain\"}, {\"type\": \"domain\"}],\n", "                                              [{\"type\": \"domain\"}, {\"type\": \"domain\"}]])\n", "    \n", "    # Add pie charts for each dimension\n", "    fig.add_trace(go.Pie(labels=['Introversion', 'Extraversion'], values=[1-e_i, e_i], \n", "                         name=\"<PERSON>/<PERSON>\", title=\"Energy\", hole=.3), 1, 1)\n", "    fig.add_trace(go.Pie(labels=['Sensing', 'Intuition'], values=[1-s_n, s_n], \n", "                         name=\"S/N\", title=\"Information\", hole=.3), 1, 2)\n", "    fig.add_trace(go.Pie(labels=['Thinking', 'Feeling'], values=[1-t_f, t_f], \n", "                         name=\"T/F\", title=\"Decisions\", hole=.3), 2, 1)\n", "    fig.add_trace(go.Pie(labels=['Judging', 'Perceiving'], values=[1-j_p, j_p], \n", "                         name=\"<PERSON><PERSON><PERSON>\", title=\"Lifestyle\", hole=.3), 2, 2)\n", "    \n", "    # Update layout\n", "    fig.update_layout(title_text=f\"MBTI Type: {mbti_type}\")\n", "    \n", "    return fig"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 3. Comprehensive Analysis Function"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def analyze_personality(text):\n", "    \"\"\"Perform a comprehensive personality analysis on the given text.\"\"\"\n", "    print(\"\\n=== Analyzing Personality ===\\n\")\n", "    \n", "    # Predict Big Five traits\n", "    big_five = predictor.predict_big_five(text)\n", "    print(\"Big Five Traits:\")\n", "    traits = [\"Extraversion\", \"Neuroticism\", \"Agreeableness\", \"Conscientiousness\", \"Openness\"]\n", "    for i, trait in enumerate(traits):\n", "        value = \"High\" if big_five[i] == 1 else \"Low\"\n", "        print(f\"  {trait}: {value}\")\n", "    \n", "    # Predict MBTI type\n", "    mbti = predictor.predict_mbti(text)\n", "    print(f\"\\nMBTI Type: {mbti}\")\n", "    \n", "    # Get career suggestions\n", "    careers = predictor.suggest_careers(big_five, mbti)\n", "    print(\"\\nCareer Suggestions:\")\n", "    for career in careers:\n", "        print(f\"  - {career}\")\n", "    \n", "    # Analyze communication style\n", "    comm_style = predictor.analyze_communication_style(big_five, mbti)\n", "    print(f\"\\nCommunication Style: {comm_style['primary_style']}\")\n", "    print(\"Strengths:\")\n", "    for strength in comm_style['strengths']:\n", "        print(f\"  - {strength}\")\n", "    print(\"Tips:\")\n", "    for tip in comm_style['tips'][:2]:  # Show just 2 tips for brevity\n", "        print(f\"  - {tip}\")\n", "    \n", "    # Analyze leadership style\n", "    lead_style = predictor.analyze_leadership_style(big_five, mbti)\n", "    print(f\"\\nLeadership Style: {lead_style['primary_style']}\")\n", "    print(\"Strengths:\")\n", "    for strength in lead_style['strengths']:\n", "        print(f\"  - {strength}\")\n", "    print(\"Tips:\")\n", "    for tip in lead_style['tips'][:2]:  # Show just 2 tips for brevity\n", "        print(f\"  - {tip}\")\n", "    \n", "    # Visualize Big Five traits\n", "    big_five_fig = visualize_big_five(big_five)\n", "    big_five_fig.show()\n", "    \n", "    # Visualize MBTI type\n", "    mbti_fig = visualize_mbti(mbti)\n", "    mbti_fig.show()\n", "    \n", "    return {\n", "        'big_five': big_five,\n", "        'mbti': mbti,\n", "        'careers': careers,\n", "        'communication_style': comm_style,\n", "        'leadership_style': lead_style\n", "    }"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 4. Example Analysis\n", "\n", "Let's analyze an example text to see how the improved system works."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example text with introverted tendencies\n", "introverted_text = \"\"\"I enjoy spending time alone, reading books and reflecting on ideas. \n", "I prefer deep one-on-one conversations to large social gatherings. \n", "I'm methodical and organized in my approach to tasks. \n", "I like to think things through carefully before making decisions.\n", "I find that I need quiet time to recharge after social events.\n", "I'm interested in understanding how things work and exploring complex concepts.\"\"\"\n", "\n", "# Analyze the introverted text\n", "introverted_results = analyze_personality(introverted_text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 5. Another Example\n", "\n", "Let's analyze a text with more extraverted tendencies to see the difference."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Example text with extraverted tendencies\n", "extraverted_text = \"\"\"I love meeting new people and attending social events! \n", "I get energized by being around others and enjoy leading group discussions. \n", "I'm spontaneous and like to try new things without too much planning. \n", "I prefer working in teams rather than alone, as I find collaboration stimulating. \n", "I'm outgoing and find it easy to start conversations with strangers. \n", "I enjoy being the center of attention and sharing my ideas with others.\"\"\"\n", "\n", "# Analyze the extraverted text\n", "extraverted_results = analyze_personality(extraverted_text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 6. Your Personality Analysis\n", "\n", "Now it's your turn! Enter your own text below to get a comprehensive personality analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Enter your text here\n", "your_text = \"\"\"I enjoy spending time thinking about new ideas, especially ones that connect different subjects or challenge how things are usually done. I’m naturally curious and love asking questions — not to be difficult, but because I genuinely want to understand how and why things work. When solving problems, I prefer to brainstorm multiple solutions before picking the best one, and I usually go with what makes the most logical sense rather than just what feels good. I get energy from being around people, especially when the conversation is stimulating or creative, but I also enjoy time alone to reset and reflect. I like activities that let me express myself — like writing, talking, or working on group projects where I can take the lead or share ideas. I often make decisions by weighing possibilities and imagining outcomes, though sometimes I struggle with overthinking because I want to keep every door open.\"\"\"\n", "\n", "# Analyze your text\n", "your_results = analyze_personality(your_text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 7. Compare Results\n", "\n", "Let's compare your results with the example personalities."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Create a comparison figure for Big Five traits\n", "fig = go.Figure()\n", "\n", "# Add traces for each personality\n", "personalities = [\n", "    (introverted_results['big_five'], \"Introverted Example\"),\n", "    (extraverted_results['big_five'], \"Extraverted Example\"),\n", "    (your_results['big_five'], \"Your Personality\")\n", "]\n", "\n", "for big_five, label in personalities:\n", "    # Add the first trait again at the end to close the polygon\n", "    r = big_five + [big_five[0]]\n", "    theta = [\"Extraversion\", \"Neuroticism\", \"Agreeableness\", \"Conscientiousness\", \"Openness\", \"Extraversion\"]\n", "    \n", "    fig.add_trace(go.<PERSON>att<PERSON>ar(\n", "        r=r,\n", "        theta=theta,\n", "        fill='toself',\n", "        name=label\n", "    ))\n", "\n", "fig.update_layout(\n", "    polar=dict(\n", "        radialaxis=dict(visible=True, range=[0, 1]),\n", "        angularaxis=dict(direction=\"clockwise\", rotation=90)\n", "    ),\n", "    title=\"Personality Comparison\"\n", ")\n", "\n", "fig.show()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 8. Career Comparison"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compare career suggestions\n", "print(\"Career Suggestions Comparison:\\n\")\n", "\n", "print(\"Introverted Example:\")\n", "for career in introverted_results['careers']:\n", "    print(f\"  - {career}\")\n", "\n", "print(\"\\nExtraverted Example:\")\n", "for career in extraverted_results['careers']:\n", "    print(f\"  - {career}\")\n", "\n", "print(\"\\nYour Personality:\")\n", "for career in your_results['careers']:\n", "    print(f\"  - {career}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 9. Communication and Leadership Style Comparison"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Compare communication styles\n", "print(\"Communication Style Comparison:\\n\")\n", "print(f\"Introverted Example: {introverted_results['communication_style']['primary_style']}\")\n", "print(f\"Extraverted Example: {extraverted_results['communication_style']['primary_style']}\")\n", "print(f\"Your Personality: {your_results['communication_style']['primary_style']}\")\n", "\n", "# Compare leadership styles\n", "print(\"\\nLeadership Style Comparison:\\n\")\n", "print(f\"Introverted Example: {introverted_results['leadership_style']['primary_style']}\")\n", "print(f\"Extraverted Example: {extraverted_results['leadership_style']['primary_style']}\")\n", "print(f\"Your Personality: {your_results['leadership_style']['primary_style']}\")"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 10. Interactive Text Analysis\n", "\n", "Use this cell to analyze any text you want. This is useful for experimenting with different descriptions to see how they affect the personality analysis."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["# Enter any text you want to analyze\n", "new_text = \"\"\"I am an introvert\"\"\"\n", "\n", "# Analyze the text\n", "new_results = analyze_personality(new_text)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 11. Personality Insights for Interviews\n", "\n", "This section provides insights that can be particularly valuable in interview situations."]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def generate_interview_insights(results):\n", "    \"\"\"Generate interview-focused insights based on personality analysis.\"\"\"\n", "    big_five = results['big_five']\n", "    mbti = results['mbti']\n", "    \n", "    print(\"\\n=== Personality Insights for Interviews ===\\n\")\n", "    \n", "    # Work style insights\n", "    print(\"Work Style:\")\n", "    if big_five[0] == 1:  # High Extraversion\n", "        print(\"- You thrive in collaborative environments and team settings\")\n", "        print(\"- You communicate ideas effectively and enjoy brainstorming sessions\")\n", "    else:  # Low Extraversion\n", "        print(\"- You excel at focused, independent work requiring deep concentration\")\n", "        print(\"- You provide thoughtful, well-considered contributions to discussions\")\n", "    \n", "    if big_five[3] == 1:  # High Conscientiousness\n", "        print(\"- You're highly organized and reliable, meeting deadlines consistently\")\n", "        print(\"- You pay close attention to details and quality standards\")\n", "    else:  # Low Conscientiousness\n", "        print(\"- You adapt quickly to changing priorities and requirements\")\n", "        print(\"- You bring a flexible, creative approach to problem-solving\")\n", "    \n", "    if big_five[4] == 1:  # High Openness\n", "        print(\"- You're innovative and bring fresh perspectives to challenges\")\n", "        print(\"- You're comfortable with ambiguity and exploring new approaches\")\n", "    \n", "    # Strengths to highlight\n", "    print(\"\\nStrengths to Highlight in Interviews:\")\n", "    strengths = []\n", "    \n", "    # MBTI-based strengths\n", "    if 'NT' in mbti:  # INTJ, INTP, ENTJ, ENTP\n", "        strengths.append(\"Strategic thinking and analytical problem-solving\")\n", "    if 'NF' in mbti:  # <PERSON>FJ, INFP, ENFJ, ENFP\n", "        strengths.append(\"Understanding people's needs and building strong relationships\")\n", "    if 'SJ' in mbti:  # ISTJ, ISFJ, ESTJ, ESFJ\n", "        strengths.append(\"Reliability and practical implementation of systems and processes\")\n", "    if 'SP' in mbti:  # ISTP, ISFP, ESTP, ESFP\n", "        strengths.append(\"Adaptability and hands-on problem-solving in the moment\")\n", "    \n", "    # Big Five based strengths\n", "    if big_five[2] == 1:  # High Agreeableness\n", "        strengths.append(\"Team building and creating harmonious work environments\")\n", "    else:  # Low Agreeableness\n", "        strengths.append(\"Making objective decisions and providing direct feedback\")\n", "    \n", "    if big_five[1] == 0:  # Low Neuroticism\n", "        strengths.append(\"Staying calm under pressure and managing stress effectively\")\n", "    \n", "    for strength in strengths:\n", "        print(f\"- {strength}\")\n", "    \n", "    # Potential interview questions\n", "    print(\"\\nPotential Interview Questions to Prepare For:\")\n", "    questions = []\n", "    \n", "    # General questions for everyone\n", "    questions.append(\"Tell me about a time you faced a significant challenge at work. How did you approach it?\")\n", "    \n", "    # Personality-specific questions\n", "    if big_five[0] == 0:  # Low Extraversion\n", "        questions.append(\"How do you ensure your ideas are heard in team settings?\")\n", "    else:  # High Extraversion\n", "        questions.append(\"How do you ensure you're listening to quieter team members?\")\n", "    \n", "    if big_five[3] == 0:  # Low Conscientiousness\n", "        questions.append(\"How do you stay organized and meet deadlines?\")\n", "    else:  # High Conscientiousness\n", "        questions.append(\"How do you adapt when plans need to change unexpectedly?\")\n", "    \n", "    if 'J' in mbti:  # Judging preference\n", "        questions.append(\"Tell me about a time when you had to work without clear structure or direction.\")\n", "    else:  # Perceiving preference\n", "        questions.append(\"How do you ensure you meet deadlines and commitments?\")\n", "    \n", "    for question in questions:\n", "        print(f\"- {question}\")\n", "    \n", "    # How to present yourself\n", "    print(\"\\nHow to Present Your Personality as a Strength:\")\n", "    print(f\"- Your {results['communication_style']['primary_style']} communication style helps you {results['communication_style']['strengths'][0].lower()}\")\n", "    print(f\"- As a {results['leadership_style']['primary_style']}, you excel at {results['leadership_style']['strengths'][0].lower()}\")\n", "    \n", "    # Career alignment\n", "    print(\"\\nCareer Alignment:\")\n", "    print(\"Your personality is well-suited for roles such as:\")\n", "    for career in results['careers'][:3]:\n", "        print(f\"- {career}\")\n", "\n", "# Generate interview insights based on your personality\n", "generate_interview_insights(your_results)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["## 12. Conc<PERSON>\n", "\n", "This notebook provides a comprehensive personality analysis that goes beyond the basic Big Five traits. The improvements include:\n", "\n", "1. **Balanced Extraversion prediction** - Using lexical analysis to correct the bias in the original model\n", "2. **Text-based Neuroticism estimation** - Using lexical analysis instead of a default value\n", "3. **Additional personality insights** - MBTI type, career suggestions, communication style, and leadership style\n", "4. **Interview-focused insights** - Specific strengths to highlight, potential questions to prepare for, and how to present your personality as an asset\n", "\n", "These enhancements make the analysis more accurate, comprehensive, and useful, especially in interview situations.\n", "\n", "To get the most accurate results, provide a detailed and authentic description of yourself, including how you think, feel, and behave in different situations."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.3"}}, "nbformat": 4, "nbformat_minor": 4}