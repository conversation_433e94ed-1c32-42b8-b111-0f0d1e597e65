#!/usr/bin/env python3
"""
Test script for ISTJ detection in the personality analysis system.
"""

from simple_personality_predictor import SimplePersonalityPredictor

def test_istj_detection():
    """Test the personality predictor with ISTJ text."""
    
    # Initialize the predictor
    predictor = SimplePersonalityPredictor()
    
    # ISTJ text from the user
    istj_text = """I take responsibility seriously and do what's right.
Structure, facts, and reliability keep things steady.
I don't need the spotlight—I just get things done.
You'll always know where I stand: steady, honest, and true."""
    
    # Analyze the text
    print("=== Analyzing ISTJ Text ===\n")
    
    # Predict Big Five traits
    big_five = predictor.predict_big_five(istj_text)
    print("Big Five Traits:")
    traits = ["Extraversion", "Neuroticism", "Agreeableness", "Conscientiousness", "Openness"]
    for i, trait in enumerate(traits):
        value = "High" if big_five[i] == 1 else "Low"
        print(f"  {trait}: {value}")
    
    # Predict MBTI type
    mbti = predictor.predict_mbti(istj_text)
    print(f"\nMBTI Type: {mbti}")
    
    # Check if prediction is correct
    is_correct = mbti == "ISTJ"
    print(f"Correct ISTJ prediction: {'✓' if is_correct else '✗'}")
    
    # Get career suggestions
    careers = predictor.suggest_careers(big_five, mbti)
    print("\nCareer Suggestions:")
    for career in careers:
        print(f"  - {career}")

if __name__ == "__main__":
    test_istj_detection()
