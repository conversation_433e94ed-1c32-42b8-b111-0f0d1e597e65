{"cells": [{"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import plotly\n", "import plotly.express as px\n", "import plotly.graph_objects as go\n", "import glob\n", "import os\n", "import pickle\n", "\n"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [], "source": ["my_path = os.path.abspath(os.path.dirname('evaluation'))\n", "eval_dir = os.path.join(my_path, \"data\\evaluation\\\\\")"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [], "source": ["files = [f for f in glob.glob(eval_dir + \"**/*.p\", recursive=True)]\n", "\n", "evals = []\n", "for f in files:\n", "    x = pickle.load(open( f, \"rb\"))\n", "    for e in x:\n", "        evals.append(e)"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["     entries vectorizer   alg trait     score\n", "109    88687      GloVe    RF  cOPN  0.785940\n", "93     88687      GloVe   svm  cOPN  0.780020\n", "105    88687      GloVe  logR  cOPN  0.777709\n", "106    88687      GloVe    RF  cEXT  0.771846\n", "101    88687      GloVe   gNB  cOPN  0.754989\n", "102    88687      GloVe  logR  cEXT  0.743489\n", "90     88687      GloVe   svm  cEXT  0.735145\n", "107    88687      GloVe    RF  cAGR  0.732326\n", "103    88687      GloVe  logR  cAGR  0.713102\n", "97     88687      GloVe  tree  cOPN  0.692017\n", "98     88687      GloVe   gNB  cEXT  0.685083\n", "91     88687      GloVe   svm  cAGR  0.680404\n", "94     88687      GloVe  tree  cEXT  0.676626\n", "95     88687      GloVe  tree  cAGR  0.663491\n", "108    88687      GloVe    RF  cCON  0.621829\n", "92     88687      GloVe   svm  cCON  0.589187\n", "104    88687      GloVe  logR  cCON  0.585917\n", "96     88687      GloVe  tree  cCON  0.566806\n", "100    88687      GloVe   gNB  cCON  0.545326\n", "99     88687      GloVe   gNB  cAGR  0.503721\n", "23     11142      GloVe   svm  cOPN  0.803948\n", "35     11142      GloVe  logR  cOPN  0.803948\n", "39     11142      GloVe    RF  cOPN  0.803948\n", "31     11142      GloVe   gNB  cOPN  0.763122\n", "21     11142      GloVe   svm  cAGR  0.740242\n", "32     11142      GloVe  logR  cEXT  0.736653\n", "33     11142      GloVe  logR  cAGR  0.729475\n", "20     11142      GloVe   svm  cEXT  0.728129\n", "36     11142      GloVe    RF  cEXT  0.715568\n", "27     11142      GloVe  tree  cOPN  0.703903\n", "37     11142      GloVe    RF  cAGR  0.688201\n", "28     11142      GloVe   gNB  cEXT  0.683715\n", "29     11142      GloVe   gNB  cAGR  0.676088\n", "24     11142      GloVe  tree  cEXT  0.641992\n", "34     11142      GloVe  logR  cCON  0.612382\n", "25     11142      GloVe  tree  cAGR  0.604755\n", "22     11142      GloVe   svm  cCON  0.604307\n", "38     11142      GloVe    RF  cCON  0.596231\n", "30     11142      GloVe   gNB  cCON  0.552266\n", "26     11142      GloVe  tree  cCON  0.536115\n", "89      2467      GloVe    RF  cOPN  0.625506\n", "81      2467      GloVe  logR  cNEU  0.617409\n", "66      2467      GloVe   svm  cNEU  0.613360\n", "69      2467      GloVe   svm  cOPN  0.613360\n", "86      2467      GloVe    RF  cNEU  0.611336\n", "84      2467      GloVe  logR  cOPN  0.609312\n", "76      2467      GloVe   gNB  cNEU  0.593117\n", "71      2467      GloVe  tree  cNEU  0.591093\n", "80      2467      GloVe  logR  cEXT  0.572874\n", "67      2467      GloVe   svm  cAGR  0.570850\n", "83      2467      GloVe  logR  cCON  0.566802\n", "65      2467      GloVe   svm  cEXT  0.562753\n", "68      2467      GloVe   svm  cCON  0.562753\n", "79      2467      GloVe   gNB  cOPN  0.558704\n", "82      2467      GloVe  logR  cAGR  0.556680\n", "78      2467      GloVe   gNB  cCON  0.544534\n", "87      2467      GloVe    RF  cAGR  0.540486\n", "77      2467      GloVe   gNB  cAGR  0.530364\n", "70      2467      GloVe  tree  cEXT  0.524291\n", "88      2467      GloVe    RF  cCON  0.524291\n", "85      2467      GloVe    RF  cEXT  0.520243\n", "72      2467      GloVe  tree  cAGR  0.506073\n", "75      2467      GloVe   gNB  cEXT  0.506073\n", "74      2467      GloVe  tree  cOPN  0.504049\n", "73      2467      GloVe  tree  cCON  0.481781\n", "113    89364        BoW  logR  cOPN  0.785710\n", "117    89364        BoW    RF  cOPN  0.785095\n", "110    89364        BoW  logR  cEXT  0.768757\n", "114    89364        BoW    RF  cEXT  0.767415\n", "111    89364        BoW  logR  cAGR  0.755105\n", "121    89364        BoW  tree  cOPN  0.745034\n", "115    89364        BoW    RF  cAGR  0.732949\n", "118    89364        BoW  tree  cEXT  0.725004\n", "119    89364        BoW  tree  cAGR  0.685727\n", "112    89364        BoW  logR  cCON  0.672019\n", "116    89364        BoW    RF  cCON  0.638225\n", "120    89364        BoW  tree  cCON  0.596934\n", "15     11142        BoW  logR  cOPN  0.790040\n", "19     11142        BoW    RF  cOPN  0.786451\n", "11     11142        BoW   gNB  cOPN  0.783760\n", "7      11142        BoW  tree  cOPN  0.773890\n", "3      11142        BoW   svm  cOPN  0.770749\n", "12     11142        BoW  logR  cEXT  0.759085\n", "13     11142        BoW  logR  cAGR  0.735756\n", "0      11142        BoW   svm  cEXT  0.734410\n", "4      11142        BoW  tree  cEXT  0.724092\n", "1      11142        BoW   svm  cAGR  0.712876\n", "16     11142        BoW    RF  cEXT  0.712876\n", "17     11142        BoW    RF  cAGR  0.711530\n", "8      11142        BoW   gNB  cEXT  0.707941\n", "14     11142        BoW  logR  cCON  0.703454\n", "2      11142        BoW   svm  cCON  0.681023\n", "5      11142        BoW  tree  cAGR  0.674742\n", "6      11142        BoW  tree  cCON  0.651413\n", "18     11142        BoW    RF  cCON  0.589502\n", "10     11142        BoW   gNB  cCON  0.569762\n", "9      11142        BoW   gNB  cAGR  0.561238\n", "64      2467        BoW    RF  cOPN  0.597166\n", "61      2467        BoW    RF  cNEU  0.564777\n", "59      2467        BoW  logR  cOPN  0.562753\n", "60      2467        BoW    RF  cEXT  0.554656\n", "43      2467        BoW   svm  cCON  0.552632\n", "58      2467        BoW  logR  cCON  0.552632\n", "44      2467        BoW   svm  cOPN  0.546559\n", "62      2467        BoW    RF  cAGR  0.542510\n", "45      2467        BoW  tree  cEXT  0.538462\n", "63      2467        BoW    RF  cCON  0.538462\n", "49      2467        BoW  tree  cOPN  0.532389\n", "48      2467        BoW  tree  cCON  0.530364\n", "55      2467        BoW  logR  cEXT  0.530364\n", "56      2467        BoW  logR  cNEU  0.528340\n", "41      2467        BoW   svm  cNEU  0.522267\n", "42      2467        BoW   svm  cAGR  0.520243\n", "52      2467        BoW   gNB  cAGR  0.520243\n", "54      2467        BoW   gNB  cOPN  0.520243\n", "40      2467        BoW   svm  cEXT  0.518219\n", "51      2467        BoW   gNB  cNEU  0.516194\n", "47      2467        BoW  tree  cAGR  0.512146\n", "57      2467        BoW  logR  cAGR  0.510121\n", "50      2467        BoW   gNB  cEXT  0.504049\n", "53      2467        BoW   gNB  cCON  0.504049\n", "46      2467        BoW  tree  cNEU  0.479757\n"]}], "source": ["df = pd.DataFrame(evals)\n", "df.columns = ['entries', 'vectorizer', 'alg', 'trait', 'score']\n", "temp = df.sort_values(by=['vectorizer', 'entries', 'score'], ascending=False)\n", "with pd.option_context('display.max_rows', None, 'display.max_columns', None):  # more options can be specified also\n", "    print(temp)\n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.8.1"}}, "nbformat": 4, "nbformat_minor": 2}